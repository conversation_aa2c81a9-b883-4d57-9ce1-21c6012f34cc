# 第三方API接口使用指南

## 概述

本文档描述了OAuth2认证中心系统为第三方系统提供的用户信息和组织机构信息API接口。这些接口需要通过OAuth2认证获取有效的access_token才能访问。

## 认证方式

### 1. 获取Access Token

首先需要通过OAuth2的client_credentials模式获取access_token：

```bash
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET
```

响应示例：
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "scope": "web"
}
```

### 2. 使用Access Token

在调用API时，需要在请求头中携带access_token：

```bash
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## API接口列表

### 用户信息相关接口

#### 1. 分页查询用户信息

**接口地址：** `GET /api/third-party/users`

**请求参数：**
- `page` (可选): 页码，默认1
- `size` (可选): 每页大小，默认10，最大100
- `humancode` (可选): 用户代码，支持模糊查询
- `humanname` (可选): 用户名称，支持模糊查询
- `orgId` (可选): 组织机构ID

**请求示例：**
```bash
GET /api/third-party/users?page=1&size=10&humanname=张三
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**响应示例：**
```json
{
  "code": "00000",
  "info": {
    "records": [
      {
        "id": "user123",
        "humancode": "zhangsan",
        "humanname": "张三",
        "email": "<EMAIL>",
        "telmobile1": "13800138000",
        "organizationnames": "技术部",
        "organizations": [
          {
            "id": "org123",
            "orgname": "技术部",
            "orgshortname": "技术部"
          }
        ]
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 2. 根据用户ID获取用户信息

**接口地址：** `GET /api/third-party/users/{userId}`

**路径参数：**
- `userId`: 用户ID

**请求示例：**
```bash
GET /api/third-party/users/user123
Authorization: Bearer YOUR_ACCESS_TOKEN
```

#### 3. 根据用户代码获取用户信息

**接口地址：** `GET /api/third-party/users/by-code/{humancode}`

**路径参数：**
- `humancode`: 用户代码

**请求示例：**
```bash
GET /api/third-party/users/by-code/zhangsan
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 组织机构相关接口

#### 1. 分页查询组织机构信息

**接口地址：** `GET /api/third-party/organizations`

**请求参数：**
- `page` (可选): 页码，默认1
- `size` (可选): 每页大小，默认10，最大100
- `orgname` (可选): 机构名称，支持模糊查询
- `parentId` (可选): 上级机构ID

**请求示例：**
```bash
GET /api/third-party/organizations?page=1&size=10&orgname=技术部
Authorization: Bearer YOUR_ACCESS_TOKEN
```

#### 2. 根据机构ID获取组织机构信息

**接口地址：** `GET /api/third-party/organizations/{orgId}`

**路径参数：**
- `orgId`: 机构ID

#### 3. 获取组织机构树形结构

**接口地址：** `GET /api/third-party/organizations/tree`

**请求参数：**
- `parentId` (可选): 父级机构ID，为空时获取根节点

**请求示例：**
```bash
GET /api/third-party/organizations/tree
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**响应示例：**
```json
{
  "code": "00000",
  "info": [
    {
      "id": "org1",
      "orgname": "总公司",
      "children": [
        {
          "id": "org2",
          "orgname": "技术部",
          "parent": "org1",
          "children": []
        }
      ]
    }
  ]
}
```

### 关联查询接口

#### 1. 根据用户ID获取用户所属组织机构列表

**接口地址：** `GET /api/third-party/users/{userId}/organizations`

**路径参数：**
- `userId`: 用户ID

#### 2. 根据组织机构ID获取该机构下的用户列表

**接口地址：** `GET /api/third-party/organizations/{orgId}/users`

**路径参数：**
- `orgId`: 组织机构ID

**请求参数：**
- `page` (可选): 页码，默认1
- `size` (可选): 每页大小，默认10，最大100

### 健康检查接口

#### API健康检查

**接口地址：** `GET /api/third-party/health`

**请求示例：**
```bash
GET /api/third-party/health
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**响应示例：**
```json
{
  "code": "00000",
  "info": "Third Party API Service is running"
}
```

## 响应格式

所有API接口都使用统一的响应格式：

```json
{
  "code": "响应码",
  "info": "响应数据或错误信息"
}
```

**响应码说明：**
- `00000`: 成功
- `00001`: 失败

## 错误处理

当请求出现错误时，会返回相应的错误信息：

```json
{
  "code": "00001",
  "info": "错误描述信息"
}
```

常见错误：
- `401 Unauthorized`: access_token无效或已过期
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误

## 数据字段说明

### 用户信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 用户ID |
| humancode | String | 用户代码/登录账号 |
| humanname | String | 用户名称 |
| email | String | 电子邮件 |
| telmobile1 | String | 手机号码1 |
| sex | String | 性别 |
| organizationnames | String | 用户所属机构名称 |
| organizations | Array | 用户所属组织机构列表 |

### 组织机构信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 机构ID |
| code | String | 机构编码 |
| orgname | String | 机构名称 |
| orgshortname | String | 机构简称 |
| parent | String | 上级机构ID |
| children | Array | 子机构列表 |

## 注意事项

1. **安全性**：所有接口都需要有效的OAuth2 access_token认证
2. **限流**：建议合理控制API调用频率，避免对系统造成压力
3. **分页**：分页查询接口的每页最大大小限制为100条
4. **数据过滤**：用户信息已过滤密码等敏感字段
5. **缓存**：建议对不经常变化的数据进行适当缓存

## 示例代码

### Java示例

```java
// 使用Spring RestTemplate调用API
RestTemplate restTemplate = new RestTemplate();
HttpHeaders headers = new HttpHeaders();
headers.setBearerAuth("YOUR_ACCESS_TOKEN");
HttpEntity<String> entity = new HttpEntity<>(headers);

ResponseEntity<String> response = restTemplate.exchange(
    "http://your-domain/api/third-party/users?page=1&size=10",
    HttpMethod.GET,
    entity,
    String.class
);
```

### Python示例

```python
import requests

headers = {
    'Authorization': 'Bearer YOUR_ACCESS_TOKEN'
}

response = requests.get(
    'http://your-domain/api/third-party/users',
    headers=headers,
    params={'page': 1, 'size': 10}
)

data = response.json()
```

## 联系方式

如有问题或需要技术支持，请联系系统管理员。
