2025-08-04 10:28:04.303 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 10:28:04.575 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 27445 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-04 10:28:04.575 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "prd"
2025-08-04 10:28:06.042 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:28:06.046 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-04 10:28:06.189 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 118 ms. Found 2 LDAP repository interfaces.
2025-08-04 10:28:06.214 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:28:06.215 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 10:28:06.224 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:28:06.225 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:28:06.225 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 MongoDB repository interfaces.
2025-08-04 10:28:06.240 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:28:06.242 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:28:06.258 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:28:06.258 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:28:06.259 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-08-04 10:28:07.080 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:28:07.109 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:28:07.146 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:28:07.985 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8282 (http)
2025-08-04 10:28:08.013 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8282"]
2025-08-04 10:28:08.014 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 10:28:08.014 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-04 10:28:08.273 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 10:28:08.273 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3616 ms
2025-08-04 10:28:09.050 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:28:09.051 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:28:09.052 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:28:11.093 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[**************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-04 10:28:14.866 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/web/static/'] with []
2025-08-04 10:28:14.966 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-04 10:28:14.990 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-04 10:28:15.018 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-04 10:28:21.231 [cluster-ClusterId{value='68901abb938a07695ea1d799', description='null'}-**************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server **************:13337
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 4 common frames omitted
2025-08-04 10:28:27.309 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-04 10:28:27.330 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 10:29:01.538 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 10:29:01.823 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 27711 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-04 10:29:01.823 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "prd"
2025-08-04 10:29:03.263 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:29:03.266 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-04 10:29:03.402 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 115 ms. Found 2 LDAP repository interfaces.
2025-08-04 10:29:03.429 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:29:03.429 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 10:29:03.440 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:29:03.440 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:29:03.441 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 MongoDB repository interfaces.
2025-08-04 10:29:03.457 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:29:03.459 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:29:03.475 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:29:03.475 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:29:03.475 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-08-04 10:29:04.123 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:29:04.137 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:29:04.167 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:29:04.597 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8282 (http)
2025-08-04 10:29:04.622 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8282"]
2025-08-04 10:29:04.622 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 10:29:04.623 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-04 10:29:04.758 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 10:29:04.759 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2831 ms
2025-08-04 10:29:05.385 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:29:05.386 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:29:05.386 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:29:07.178 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[**************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-04 10:29:09.818 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/web/static/'] with []
2025-08-04 10:29:09.891 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-04 10:29:09.909 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-04 10:29:09.934 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-04 10:29:17.302 [cluster-ClusterId{value='68901af3b864062fd13ca1e4', description='null'}-**************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server **************:13337
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 4 common frames omitted
2025-08-04 10:29:22.215 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-04 10:29:22.236 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 10:30:01.058 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 10:30:01.329 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 27836 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-04 10:30:01.330 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "prd"
2025-08-04 10:30:02.699 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:30:02.703 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-04 10:30:02.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 114 ms. Found 2 LDAP repository interfaces.
2025-08-04 10:30:02.865 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:30:02.865 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 10:30:02.875 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:30:02.875 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:30:02.875 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 MongoDB repository interfaces.
2025-08-04 10:30:02.890 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:30:02.892 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:30:02.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:30:02.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:30:02.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-08-04 10:30:03.569 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:30:03.584 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:30:03.611 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:30:04.074 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8282 (http)
2025-08-04 10:30:04.102 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8282"]
2025-08-04 10:30:04.103 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 10:30:04.103 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-04 10:30:04.258 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 10:30:04.258 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2855 ms
2025-08-04 10:30:04.892 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:30:04.893 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:30:04.893 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:30:06.654 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[**************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-04 10:30:09.314 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/web/static/'] with []
2025-08-04 10:30:09.382 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-04 10:30:09.400 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-04 10:30:09.425 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-04 10:30:16.786 [cluster-ClusterId{value='68901b2e03cdb0634c18ee3f', description='null'}-**************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server **************:13337
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 4 common frames omitted
2025-08-04 10:30:21.632 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-04 10:30:21.653 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 10:31:07.065 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 10:31:07.335 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 28115 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-04 10:31:07.336 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "loc"
2025-08-04 10:31:08.680 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:31:08.683 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-04 10:31:08.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 116 ms. Found 2 LDAP repository interfaces.
2025-08-04 10:31:08.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:31:08.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 10:31:08.852 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:31:08.852 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-04 10:31:08.852 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 MongoDB repository interfaces.
2025-08-04 10:31:08.865 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:31:08.867 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:31:08.881 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:31:08.882 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:31:08.882 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-08-04 10:31:09.541 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:31:09.556 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:31:09.584 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:31:10.022 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-08-04 10:31:10.049 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-08-04 10:31:10.049 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 10:31:10.049 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-04 10:31:10.185 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 10:31:10.185 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2775 ms
2025-08-04 10:31:10.809 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:31:10.809 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:31:10.810 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 10:31:12.583 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-04 10:31:12.971 [cluster-rtt-ClusterId{value='68901b70fdfbd46ddc388346', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:20946}] to *************:13337
2025-08-04 10:31:12.971 [cluster-ClusterId{value='68901b70fdfbd46ddc388346', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:20947}] to *************:13337
2025-08-04 10:31:12.972 [cluster-ClusterId{value='68901b70fdfbd46ddc388346', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=148947375}
2025-08-04 10:31:15.316 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-04 10:31:15.395 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-04 10:31:15.414 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-04 10:31:15.441 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-04 10:31:17.844 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-04 10:31:17.913 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-04 10:31:17.915 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-04 10:31:17.918 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-04 10:31:18.194 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-04 10:31:18.209 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-04 10:31:18.213 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-04 10:31:18.213 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-04 10:31:18.213 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-04 10:31:18.213 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-04 10:31:18.214 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-04 10:31:18.214 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-04 10:31:18.214 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-04 10:31:18.220 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-04 10:31:18.220 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-04 10:31:18.325 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-08-04 10:31:18.367 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-08-04 10:31:18.935 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-08-04 10:31:18.956 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 12.618 seconds (JVM running for 18.761)
2025-08-04 10:31:18.972 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-04 10:31:20.078 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-08-04 10:31:20.443 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-04 10:31:25.891 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-08-04 10:31:29.328 [http-nio-8090-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 10:31:29.328 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 10:31:29.329 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-04 10:31:30.071 [http-nio-8090-exec-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:20948}] to *************:13337
2025-08-04 10:31:34.885 [http-nio-8090-exec-2] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:20952}] to *************:13337
2025-08-04 10:31:34.885 [http-nio-8090-exec-4] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:20949}] to *************:13337
2025-08-04 10:31:34.885 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:20950}] to *************:13337
2025-08-04 10:31:34.885 [http-nio-8090-exec-9] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:20951}] to *************:13337
2025-08-04 10:33:30.749 [http-nio-8090-exec-8] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:20953}] to *************:13337
2025-08-04 10:43:09.327 [http-nio-8090-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:43:09.332 [http-nio-8090-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-08-04 10:43:09.334 [http-nio-8090-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-04 10:43:09.336 [http-nio-8090-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 LDAP repository interfaces.
2025-08-04 10:43:09.336 [http-nio-8090-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 10:43:09.338 [http-nio-8090-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-08-04 10:43:27.389 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:43:28.823 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:32.318 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:36.606 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:46.813 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:49.109 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:50.148 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:50.239 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:50.280 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:46:50.412 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:20954}] to *************:13337
2025-08-04 10:46:58.311 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-04 10:47:00.436 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:01.955 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:02.877 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:04.233 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:04.445 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:04.445 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:04.445 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:04.625 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:04.625 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:06.687 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:06.866 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:10.166 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:10.333 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:10.752 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@8d285e, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:47:10 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:47:18.795 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:18.961 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:19.202 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@400653f9, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:47:19 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:47:19.640 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:19.808 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:20.091 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@3e5ec8fc, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:47:20 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:47:25.042 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:25.214 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:25.470 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@521b64f4, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:47:25 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:47:32.176 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:32.370 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:32.643 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@2ec27e01, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:47:32 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:47:38.410 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:38.656 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:47:38.890 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@2aea48da, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:47:38 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:48:24.108 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:48:24.308 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:48:24.513 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@5157051d, operator=10001, action=编辑系统参数, fail=false, createTime=Mon Aug 04 10:48:24 CST 2025, extra=, codeVariable={MethodName=edit, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:48:25.926 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:48:26.091 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:48:26.377 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@589cdbb6, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:48:26 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:48:36.504 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:48:41.461 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:15.303 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:18.943 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-04 10:49:20.457 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:21.390 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:22.333 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:22.400 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:22.432 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:22.492 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:22.602 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:22.605 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:23.298 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:23.471 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:23.763 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@7a487422, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:49:23 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:49:32.368 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:32.511 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:32.757 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@2421e6a0, operator=10001, action=编辑系统参数, fail=false, createTime=Mon Aug 04 10:49:32 CST 2025, extra=, codeVariable={MethodName=edit, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:49:33.906 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:34.042 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:34.280 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@481be976, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:49:34 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:49:42.442 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:49:44.332 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:01.721 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:06.172 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:29.466 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:32.443 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:34.792 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-04 10:50:36.666 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:38.039 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:40.918 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:40.973 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:41.001 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:41.151 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:41.234 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:41.234 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:41.956 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:42.126 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:42.366 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@6051bad, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:50:42 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:50:50.223 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:50.407 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:50.543 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@2072dc77, operator=10001, action=编辑系统参数, fail=false, createTime=Mon Aug 04 10:50:50 CST 2025, extra=, codeVariable={MethodName=edit, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:50:51.758 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:51.901 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:50:52.137 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.SytSysParam@57fdfc6d, operator=10001, action=查询系统参数, fail=false, createTime=Mon Aug 04 10:50:52 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.SytSysParamController})
2025-08-04 10:51:00.215 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 10:51:04.576 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
