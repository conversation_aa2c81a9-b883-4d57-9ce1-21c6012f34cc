2025-08-04 10:28:10.038 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.dto.StatisticsDto".
2025-08-04 10:28:10.043 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:28:10.149 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.Resource",So @TableField annotation will not work!
2025-08-04 10:28:10.166 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResourceLink".
2025-08-04 10:28:10.166 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:28:10.178 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResource".
2025-08-04 10:28:10.178 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:28:10.290 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.SytPermissionAccount",So @TableField annotation will not work!
2025-08-04 10:28:10.340 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.SytSysOrganizationUser".
2025-08-04 10:28:10.340 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:28:12.065 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-04 10:28:14.865 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/web/static/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-04 10:28:15.647 [main] WARN  org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/web/templates/ (please add some templates or check your Thymeleaf configuration)
2025-08-04 10:28:15.662 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-04 10:28:27.140 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'enableRedisKeyspaceNotificationsInitializer' defined in class path resource [org/springframework/boot/autoconfigure/session/RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration.class]: Invocation of init method failed; nested exception is org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 211.103.188.30:6379
2025-08-04 10:29:06.277 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.dto.StatisticsDto".
2025-08-04 10:29:06.277 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:29:06.381 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.Resource",So @TableField annotation will not work!
2025-08-04 10:29:06.396 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResourceLink".
2025-08-04 10:29:06.396 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:29:06.408 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResource".
2025-08-04 10:29:06.408 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:29:06.501 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.SytPermissionAccount",So @TableField annotation will not work!
2025-08-04 10:29:06.557 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.SytSysOrganizationUser".
2025-08-04 10:29:06.557 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:29:08.036 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-04 10:29:09.817 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/web/static/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-04 10:29:10.541 [main] WARN  org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/web/templates/ (please add some templates or check your Thymeleaf configuration)
2025-08-04 10:29:10.555 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-04 10:29:22.034 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'enableRedisKeyspaceNotificationsInitializer' defined in class path resource [org/springframework/boot/autoconfigure/session/RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration.class]: Invocation of init method failed; nested exception is org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 211.103.188.30:6379
2025-08-04 10:30:05.775 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.dto.StatisticsDto".
2025-08-04 10:30:05.775 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:30:05.865 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.Resource",So @TableField annotation will not work!
2025-08-04 10:30:05.880 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResourceLink".
2025-08-04 10:30:05.880 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:30:05.891 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.RoleResource".
2025-08-04 10:30:05.891 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:30:05.993 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by @TableId annotation in Class: "com.sanyth.auth.server.model.SytPermissionAccount",So @TableField annotation will not work!
2025-08-04 10:30:06.052 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.auth.server.model.SytSysOrganizationUser".
2025-08-04 10:30:06.052 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:30:07.518 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-04 10:30:09.313 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/web/static/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-04 10:30:10.011 [main] WARN  org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration - Cannot find template location: classpath:/web/templates/ (please add some templates or check your Thymeleaf configuration)
2025-08-04 10:30:10.025 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-04 10:30:21.461 [main] WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'enableRedisKeyspaceNotificationsInitializer' defined in class path resource [org/springframework/boot/autoconfigure/session/RedisSessionConfiguration$SpringBootRedisHttpSessionConfiguration.class]: Invocation of init method failed; nested exception is org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 211.103.188.30:6379
2025-08-04 10:31:11.700 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:31:11.809 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:31:11.821 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:31:11.982 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 10:31:13.501 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-04 10:31:15.315 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/static']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-04 10:31:16.045 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-04 10:31:18.972 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04 10:31:18.985 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-08-04 10:43:09.334 [http-nio-8090-exec-6] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.sanyth.auth.server]' package. Please check your configuration.
