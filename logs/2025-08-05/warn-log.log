2025-08-05 09:03:07.694 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 09:03:08.054 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 09:03:08.081 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 09:03:08.479 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 09:03:11.124 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-05 09:03:13.614 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/static']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-05 09:03:14.546 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-05 09:03:18.305 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05 09:03:18.318 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-08-05 16:36:33.895 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 16:36:34.037 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 16:36:34.068 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 16:36:34.372 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.auth.server.model.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-05 16:36:37.417 [main] WARN  com.sanyth.auth.server.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-05 16:36:39.467 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/static']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-05 16:36:40.240 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-05 16:36:47.831 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05 16:36:47.852 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
2025-08-05 17:57:29.946 [rebel-change-detector-thread] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.sanyth.auth.server]' package. Please check your configuration.
2025-08-05 18:18:23.214 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Retrograde clock change detected (housekeeper delta=29s839ms), soft-evicting connections from pool.
2025-08-05 18:39:10.224 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=2m16s859ms).
2025-08-05 18:57:25.010 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=18m14s786ms).
2025-08-05 19:11:35.158 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=14m10s148ms).
2025-08-05 19:11:48.277 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 19:12:06.666 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 19:12:16.726 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 19:28:39.241 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=15m55s806ms).
2025-08-05 19:44:10.717 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=15m31s477ms).
2025-08-05 20:02:19.457 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=17m38s730ms).
2025-08-05 20:35:40.213 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=32m50s747ms).
2025-08-05 20:53:06.618 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=16m56s394ms).
2025-08-05 21:37:03.796 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=43m27s147ms).
2025-08-05 21:46:47.274 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=9m13s467ms).
2025-08-05 22:39:33.342 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:39:43.439 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:39:53.541 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:40:03.641 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:40:13.754 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:40:23.839 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:40:33.942 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:40:44.136 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:40:54.445 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:41:05.037 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:41:16.133 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:41:28.244 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:41:42.444 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:42:00.735 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:42:27.140 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:43:07.239 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:43:47.330 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:44:27.437 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:45:07.534 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:45:47.633 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:46:27.733 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:47:07.832 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:47:47.937 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:48:28.037 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:49:08.136 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:49:48.232 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:50:28.333 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:51:08.439 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:51:48.536 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:52:28.633 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:53:08.742 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:53:48.835 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:54:28.935 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:55:09.033 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:55:49.131 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:56:29.235 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:57:09.333 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:57:49.429 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 22:58:29.595 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:15:18.835 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=17m492ms).
2025-08-05 23:15:40.200 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:16:20.268 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:17:00.378 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:17:36.830 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=47s968ms).
2025-08-05 23:17:58.458 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:18:38.535 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:32:56.047 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:33:14.278 [DatebookHikariCP housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - DatebookHikariCP - Thread starvation or clock leap detected (housekeeper delta=14m7s426ms).
2025-08-05 23:33:36.148 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:34:16.254 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:34:56.354 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:35:36.446 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:36:16.552 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:36:56.651 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:37:36.756 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:38:16.859 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:38:56.955 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:39:37.054 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:40:17.151 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:40:57.256 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:41:37.360 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:42:17.452 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:42:57.549 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:43:37.656 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:44:17.750 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:44:57.852 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:45:37.949 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:46:18.051 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:46:58.156 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:47:38.255 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:48:18.342 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:48:58.445 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:49:38.540 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:50:18.639 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:50:58.737 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:51:38.835 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:52:18.935 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:52:59.034 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:53:39.136 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:54:19.239 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:54:59.337 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:55:39.441 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:56:19.540 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:56:59.635 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:57:39.735 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:58:19.833 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:58:59.933 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
2025-08-05 23:59:40.050 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog - Cannot reconnect to [*************:6380]: connection timed out: /*************:6380
