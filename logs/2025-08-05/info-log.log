2025-08-05 09:03:02.015 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-05 09:03:02.472 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 55400 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-05 09:03:02.472 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "loc"
2025-08-05 09:03:04.034 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 09:03:04.038 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-05 09:03:04.365 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 155 ms. Found 2 LDAP repository interfaces.
2025-08-05 09:03:04.405 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 09:03:04.405 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-05 09:03:04.417 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 09:03:04.417 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 09:03:04.418 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 MongoDB repository interfaces.
2025-08-05 09:03:04.438 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 09:03:04.440 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 09:03:04.462 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 09:03:04.462 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 09:03:04.463 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-08-05 09:03:05.232 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 09:03:05.249 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 09:03:05.279 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 09:03:05.814 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-08-05 09:03:05.852 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-08-05 09:03:05.852 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 09:03:05.852 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-05 09:03:06.005 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-05 09:03:06.005 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3403 ms
2025-08-05 09:03:06.706 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 09:03:06.707 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 09:03:06.707 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 09:03:09.793 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-05 09:03:10.202 [cluster-rtt-ClusterId{value='6891584d6842f910bcf5cfdb', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21103}] to *************:13337
2025-08-05 09:03:10.202 [cluster-ClusterId{value='6891584d6842f910bcf5cfdb', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21104}] to *************:13337
2025-08-05 09:03:10.203 [cluster-ClusterId{value='6891584d6842f910bcf5cfdb', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=147746042}
2025-08-05 09:03:13.616 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-05 09:03:13.708 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-05 09:03:13.737 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-05 09:03:13.771 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-05 09:03:16.982 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-05 09:03:17.174 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-05 09:03:17.180 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-05 09:03:17.184 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-05 09:03:17.535 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-05 09:03:17.553 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-05 09:03:17.558 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-05 09:03:17.561 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-05 09:03:17.566 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-05 09:03:17.567 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-05 09:03:17.687 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-08-05 09:03:17.727 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-08-05 09:03:18.265 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-08-05 09:03:18.286 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 17.068 seconds (JVM running for 24.344)
2025-08-05 09:03:18.305 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-05 09:03:19.369 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-08-05 09:03:19.735 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-05 09:03:24.657 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-08-05 09:10:01.099 [http-nio-8090-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 09:10:01.099 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 09:10:01.104 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-08-05 09:10:01.915 [http-nio-8090-exec-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:21112}] to *************:13337
2025-08-05 09:10:06.294 [http-nio-8090-exec-5] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:21113}] to *************:13337
2025-08-05 09:10:06.307 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:21114}] to *************:13337
2025-08-05 09:10:07.616 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:10:07.715 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:10:07.715 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:10:07.830 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:21115}] to *************:13337
2025-08-05 09:10:07.883 [http-nio-8090-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:21116}] to *************:13337
2025-08-05 09:10:07.916 [http-nio-8090-exec-7] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:21117}] to *************:13337
2025-08-05 09:10:07.916 [http-nio-8090-exec-9] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:21118}] to *************:13337
2025-08-05 09:10:08.096 [http-nio-8090-exec-12] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:21119}] to *************:13337
2025-08-05 09:11:01.055 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-05 09:11:03.274 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:04.415 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.643 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.643 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.643 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.762 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.762 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:25.287 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:25 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:25.993 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:25 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:27.074 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:27 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:27.200 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:27 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:27.280 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:27.402 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.213 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.345 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.788 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.921 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.335 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.495 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.733 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.893 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:30.411 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:30.560 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.197 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.334 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.603 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.670 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.704 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.734 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.785 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.829 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.099 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.229 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.564 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.704 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:33.126 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:33.271 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:33.653 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@55f496d3, operator=sytadmin, action=查询认证应用, fail=false, createTime=Tue Aug 05 09:11:33 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-05 09:11:44.723 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:44.852 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:45.132 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo={id=616cc15f59d2dc084301d2b7db038587}, operator=sytadmin, action=删除认证应用, fail=false, createTime=Tue Aug 05 09:11:45 CST 2025, extra=, codeVariable={MethodName=delete, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-05 09:11:46.153 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:46.279 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:46.567 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@7f2065d5, operator=sytadmin, action=查询认证应用, fail=false, createTime=Tue Aug 05 09:11:46 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-05 09:16:31.527 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:3, serverValue:21112}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:4, serverValue:21113}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:7, serverValue:21118}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:6, serverValue:21115}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:8, serverValue:21116}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:10, serverValue:21119}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:9, serverValue:21117}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:5, serverValue:21114}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.537 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown initiated...
2025-08-05 16:36:28.777 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-05 16:36:29.050 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 33905 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-05 16:36:29.050 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "dev"
2025-08-05 16:36:30.464 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:36:30.467 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-05 16:36:30.611 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 120 ms. Found 2 LDAP repository interfaces.
2025-08-05 16:36:30.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:36:30.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-05 16:36:30.648 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 16:36:30.648 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 16:36:30.648 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 MongoDB repository interfaces.
2025-08-05 16:36:30.664 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:36:30.666 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 16:36:30.683 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 16:36:30.684 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 16:36:30.684 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-08-05 16:36:31.418 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:36:31.434 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:36:31.467 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:36:31.930 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-08-05 16:36:31.957 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-08-05 16:36:31.957 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 16:36:31.957 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-05 16:36:32.102 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-05 16:36:32.103 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2974 ms
2025-08-05 16:36:32.756 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 16:36:32.756 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 16:36:32.757 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 16:36:35.490 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-05 16:36:36.076 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21291}] to *************:13337
2025-08-05 16:36:37.666 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21292}] to *************:13337
2025-08-05 16:36:37.667 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=260019500}
2025-08-05 16:36:39.468 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-05 16:36:39.550 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-05 16:36:39.574 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-05 16:36:39.601 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-05 16:36:42.148 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-05 16:36:42.223 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-05 16:36:42.226 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-05 16:36:42.228 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-05 16:36:42.475 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-05 16:36:42.491 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-05 16:36:42.497 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-05 16:36:42.502 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-05 16:36:42.503 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-05 16:36:42.613 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-08-05 16:36:42.665 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-08-05 16:36:47.726 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-08-05 16:36:47.792 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 19.741 seconds (JVM running for 26.964)
2025-08-05 16:36:47.831 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-05 16:36:51.717 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-08-05 16:36:52.122 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-05 16:36:59.543 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
