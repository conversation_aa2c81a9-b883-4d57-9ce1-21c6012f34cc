2025-08-05 09:03:02.015 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-05 09:03:02.472 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 55400 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-05 09:03:02.472 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "loc"
2025-08-05 09:03:04.034 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 09:03:04.038 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-05 09:03:04.365 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 155 ms. Found 2 LDAP repository interfaces.
2025-08-05 09:03:04.405 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 09:03:04.405 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-05 09:03:04.417 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 09:03:04.417 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 09:03:04.418 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 MongoDB repository interfaces.
2025-08-05 09:03:04.438 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 09:03:04.440 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 09:03:04.462 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 09:03:04.462 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 09:03:04.463 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-08-05 09:03:05.232 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 09:03:05.249 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 09:03:05.279 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 09:03:05.814 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-08-05 09:03:05.852 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-08-05 09:03:05.852 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 09:03:05.852 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-05 09:03:06.005 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-05 09:03:06.005 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3403 ms
2025-08-05 09:03:06.706 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 09:03:06.707 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 09:03:06.707 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 09:03:09.793 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-05 09:03:10.202 [cluster-rtt-ClusterId{value='6891584d6842f910bcf5cfdb', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21103}] to *************:13337
2025-08-05 09:03:10.202 [cluster-ClusterId{value='6891584d6842f910bcf5cfdb', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21104}] to *************:13337
2025-08-05 09:03:10.203 [cluster-ClusterId{value='6891584d6842f910bcf5cfdb', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=147746042}
2025-08-05 09:03:13.616 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-05 09:03:13.708 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-05 09:03:13.737 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-05 09:03:13.771 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-05 09:03:16.982 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-05 09:03:17.174 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-05 09:03:17.180 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-05 09:03:17.184 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-05 09:03:17.535 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-05 09:03:17.553 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-05 09:03:17.558 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-05 09:03:17.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-05 09:03:17.561 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-05 09:03:17.566 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-05 09:03:17.567 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-05 09:03:17.687 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-08-05 09:03:17.727 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-08-05 09:03:18.265 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-08-05 09:03:18.286 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 17.068 seconds (JVM running for 24.344)
2025-08-05 09:03:18.305 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-05 09:03:19.369 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-08-05 09:03:19.735 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-05 09:03:24.657 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-08-05 09:10:01.099 [http-nio-8090-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 09:10:01.099 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-05 09:10:01.104 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-08-05 09:10:01.915 [http-nio-8090-exec-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:21112}] to *************:13337
2025-08-05 09:10:06.294 [http-nio-8090-exec-5] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:21113}] to *************:13337
2025-08-05 09:10:06.307 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:21114}] to *************:13337
2025-08-05 09:10:07.616 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:10:07.715 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:10:07.715 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:10:07.830 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:21115}] to *************:13337
2025-08-05 09:10:07.883 [http-nio-8090-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:21116}] to *************:13337
2025-08-05 09:10:07.916 [http-nio-8090-exec-7] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:21117}] to *************:13337
2025-08-05 09:10:07.916 [http-nio-8090-exec-9] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:21118}] to *************:13337
2025-08-05 09:10:08.096 [http-nio-8090-exec-12] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:21119}] to *************:13337
2025-08-05 09:11:01.055 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-05 09:11:03.274 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:04.415 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.643 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.643 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.643 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.762 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:05.762 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:25.287 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:25 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:25.993 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:25 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:27.074 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:27 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:27.200 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Tue Aug 05 09:11:27 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-05 09:11:27.280 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:27.402 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.213 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.345 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.788 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:28.921 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.335 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.495 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.733 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:29.893 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:30.411 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:30.560 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.197 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.334 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.603 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.670 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.704 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.734 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.785 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:31.829 [http-nio-8090-exec-11] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.099 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.229 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.564 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:32.704 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:33.126 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:33.271 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:33.653 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@55f496d3, operator=sytadmin, action=查询认证应用, fail=false, createTime=Tue Aug 05 09:11:33 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-05 09:11:44.723 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:44.852 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:45.132 [http-nio-8090-exec-12] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo={id=616cc15f59d2dc084301d2b7db038587}, operator=sytadmin, action=删除认证应用, fail=false, createTime=Tue Aug 05 09:11:45 CST 2025, extra=, codeVariable={MethodName=delete, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-05 09:11:46.153 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:46.279 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-05 09:11:46.567 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@7f2065d5, operator=sytadmin, action=查询认证应用, fail=false, createTime=Tue Aug 05 09:11:46 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-05 09:16:31.527 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:3, serverValue:21112}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:4, serverValue:21113}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:7, serverValue:21118}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:6, serverValue:21115}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:8, serverValue:21116}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:10, serverValue:21119}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:9, serverValue:21117}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.531 [SpringApplicationShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:5, serverValue:21114}] to *************:13337 because the pool has been closed.
2025-08-05 09:16:31.537 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Shutdown initiated...
2025-08-05 16:36:28.777 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-05 16:36:29.050 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 33905 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-05 16:36:29.050 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "dev"
2025-08-05 16:36:30.464 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:36:30.467 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-05 16:36:30.611 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 120 ms. Found 2 LDAP repository interfaces.
2025-08-05 16:36:30.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:36:30.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-05 16:36:30.648 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 16:36:30.648 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-05 16:36:30.648 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 MongoDB repository interfaces.
2025-08-05 16:36:30.664 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-05 16:36:30.666 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 16:36:30.683 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 16:36:30.684 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-05 16:36:30.684 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-08-05 16:36:31.418 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:36:31.434 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:36:31.467 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-05 16:36:31.930 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-08-05 16:36:31.957 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-08-05 16:36:31.957 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 16:36:31.957 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-05 16:36:32.102 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-05 16:36:32.103 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2974 ms
2025-08-05 16:36:32.756 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 16:36:32.756 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 16:36:32.757 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-05 16:36:35.490 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-05 16:36:36.076 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21291}] to *************:13337
2025-08-05 16:36:37.666 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21292}] to *************:13337
2025-08-05 16:36:37.667 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=260019500}
2025-08-05 16:36:39.468 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-05 16:36:39.550 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-05 16:36:39.574 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-05 16:36:39.601 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-05 16:36:42.148 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-05 16:36:42.223 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-05 16:36:42.226 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-05 16:36:42.228 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-05 16:36:42.475 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-05 16:36:42.491 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-05 16:36:42.496 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-05 16:36:42.497 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-05 16:36:42.502 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-05 16:36:42.503 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-05 16:36:42.613 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-08-05 16:36:42.665 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-08-05 16:36:47.726 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-08-05 16:36:47.792 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 19.741 seconds (JVM running for 26.964)
2025-08-05 16:36:47.831 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-05 16:36:51.717 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-08-05 16:36:52.122 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-05 16:36:59.543 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-08-05 17:57:29.904 [rebel-change-detector-thread] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-05 17:57:29.927 [rebel-change-detector-thread] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-05 17:57:29.927 [rebel-change-detector-thread] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-05 17:57:29.933 [rebel-change-detector-thread] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 LDAP repository interfaces.
2025-08-05 17:57:29.934 [rebel-change-detector-thread] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-05 17:57:29.939 [rebel-change-detector-thread] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 MongoDB repository interfaces.
2025-08-05 18:22:02.189 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 18:22:02.400 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:21383}] to *************:13337
2025-08-05 18:22:02.401 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=65284042}
2025-08-05 18:22:12.654 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:21384}] to *************:13337
2025-08-05 18:22:53.638 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /*************:6380
2025-08-05 18:22:53.885 [lettuce-nioEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to *************:6380
2025-08-05 18:39:06.217 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 18:39:16.251 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Connect timed out
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:126)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:613)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 4 common frames omitted
2025-08-05 19:11:38.256 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /*************:6380
2025-08-05 19:11:48.360 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 19:12:06.717 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 19:12:12.737 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:14, serverValue:21385}] to *************:13337
2025-08-05 19:12:16.821 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 19:12:17.061 [lettuce-nioEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to *************:6380
2025-08-05 19:12:19.718 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:15, serverValue:21386}] to *************:13337
2025-08-05 19:12:19.719 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=63177584}
2025-08-05 19:28:25.714 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 19:28:25.929 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:16, serverValue:21387}] to *************:13337
2025-08-05 19:28:25.929 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=62351209}
2025-08-05 19:28:39.429 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:17, serverValue:21388}] to *************:13337
2025-08-05 19:44:06.495 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /*************:6380
2025-08-05 19:44:13.733 [lettuce-nioEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to *************:6380
2025-08-05 19:44:17.671 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 19:44:17.876 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:18, serverValue:21391}] to *************:13337
2025-08-05 19:44:17.878 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=63347333}
2025-08-05 19:44:31.348 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:19, serverValue:21392}] to *************:13337
2025-08-05 20:02:16.836 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 20:02:17.052 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:20, serverValue:21395}] to *************:13337
2025-08-05 20:02:17.053 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=66440500}
2025-08-05 20:02:20.387 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:21, serverValue:21396}] to *************:13337
2025-08-05 20:35:20.355 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /*************:6380
2025-08-05 20:35:24.888 [lettuce-nioEventLoop-4-9] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to *************:6380
2025-08-05 20:35:28.042 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 20:35:28.268 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:22, serverValue:21398}] to *************:13337
2025-08-05 20:35:28.269 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=71945459}
2025-08-05 20:35:41.599 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:23, serverValue:21399}] to *************:13337
2025-08-05 20:53:15.137 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 20:53:15.349 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:24, serverValue:21403}] to *************:13337
2025-08-05 20:53:15.349 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=68220875}
2025-08-05 20:53:18.575 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:25, serverValue:21404}] to *************:13337
2025-08-05 21:11:01.492 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /*************:6380
2025-08-05 21:29:01.498 [lettuce-nioEventLoop-4-10] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to *************:6380
2025-08-05 21:37:01.354 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:26, serverValue:21405}] to *************:13337
2025-08-05 21:37:02.762 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 21:37:03.004 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:27, serverValue:21406}] to *************:13337
2025-08-05 21:37:03.005 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=78754417}
2025-08-05 21:46:36.710 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 21:46:36.920 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:28, serverValue:21407}] to *************:13337
2025-08-05 21:46:36.921 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=65689750}
2025-08-05 21:46:43.249 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /*************:6380
2025-08-05 21:46:43.491 [lettuce-nioEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to *************:6380
2025-08-05 21:46:55.435 [cluster-rtt-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:29, serverValue:21408}] to *************:13337
2025-08-05 22:38:30.670 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:630)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:515)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:304)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:218)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	... 5 common frames omitted
2025-08-05 22:38:40.682 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Connect timed out
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:126)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:613)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 4 common frames omitted
2025-08-05 22:39:23.330 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /*************:6380
2025-08-05 22:39:33.430 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:39:43.531 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:39:53.633 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:40:03.731 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:40:13.831 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:40:23.937 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:40:34.130 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:40:44.433 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:40:55.029 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:41:06.126 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:41:18.231 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:41:32.429 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:41:50.726 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:42:17.131 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:42:57.230 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:43:37.325 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:44:17.429 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:44:57.527 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:45:37.626 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:46:17.725 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:46:57.825 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:47:37.929 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:48:18.025 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:48:58.128 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:49:38.226 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:50:18.327 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:50:58.428 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:51:38.529 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:52:18.624 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:52:58.728 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:53:38.825 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:54:18.926 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:54:59.027 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:55:39.122 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:56:19.225 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:56:59.325 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:57:39.421 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 22:58:19.582 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:15:30.162 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:16:10.262 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:16:50.368 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:17:48.422 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:18:28.525 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:19:08.625 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:33:26.142 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:34:06.244 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:34:46.337 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:35:26.440 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:36:06.543 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:36:46.639 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:37:26.741 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:38:06.843 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:38:46.944 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:39:27.044 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:40:07.141 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:40:47.242 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:41:27.343 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:42:07.442 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:42:47.541 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:43:27.639 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:44:07.742 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:44:47.843 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:45:27.942 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:46:08.041 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:46:48.146 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:47:28.244 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:48:08.341 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:48:48.433 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:49:28.532 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:50:08.629 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:50:48.730 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:51:28.828 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:52:08.928 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:52:49.027 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:53:29.130 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:54:09.232 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:54:49.330 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:55:29.435 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:56:09.533 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:56:49.629 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:57:29.730 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:58:09.828 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:58:49.929 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-05 23:59:30.032 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
