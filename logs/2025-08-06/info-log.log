2025-08-06 00:00:10.131 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:00:50.227 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:01:30.328 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:02:10.427 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:02:50.530 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:03:30.699 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:04:10.800 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:04:50.901 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:05:31.006 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:06:11.098 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:06:51.201 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:07:31.299 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:08:11.403 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:08:51.501 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:09:31.601 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:10:11.707 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:10:51.805 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:11:16.638 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-08-06 00:11:31.904 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:11:36.652 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Connect timed out
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:126)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:613)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 4 common frames omitted
2025-08-06 00:12:12.003 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:12:52.104 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:13:21.710 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:131)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:647)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:512)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:355)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:279)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:107)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:62)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:144)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
2025-08-06 00:13:32.207 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:13:41.723 [cluster-ClusterId{value='6891c2933c0abf7fd33e16f8', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server *************:13337
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:143)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:188)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:144)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Connect timed out
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:126)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:613)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:107)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 4 common frames omitted
2025-08-06 00:14:12.304 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:14:52.406 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:15:32.507 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:16:12.609 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:16:52.705 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:17:32.813 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:18:12.908 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:18:53.010 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:19:33.109 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:20:13.207 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:20:53.311 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:21:33.411 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:22:13.511 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:22:53.610 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:23:33.709 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:24:13.816 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:24:53.911 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:25:34.011 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:26:14.112 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:26:54.212 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:27:34.314 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:28:14.414 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:28:54.514 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:29:34.614 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:30:14.716 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:30:54.816 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:31:34.916 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:32:15.015 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:32:55.118 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:33:35.216 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:34:15.319 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:34:55.417 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:35:35.519 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:36:15.617 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:36:55.721 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:37:35.816 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:38:15.918 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:38:56.022 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:39:36.121 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:40:16.218 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:40:56.321 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:41:36.423 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:42:16.520 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:42:56.625 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:43:36.722 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:44:16.825 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:44:56.929 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:45:37.024 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:46:17.123 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:46:57.226 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:47:37.324 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:48:17.426 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:48:57.528 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:49:37.627 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:50:17.728 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:50:57.828 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:51:37.929 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:52:18.028 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:52:58.129 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:53:38.230 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:54:18.332 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:54:58.431 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:55:38.531 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:56:18.633 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:56:58.731 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:57:38.832 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:58:18.935 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:58:59.033 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 00:59:39.137 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:00:19.233 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:00:59.336 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:01:39.433 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:02:19.537 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:02:59.635 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:03:39.736 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:04:19.837 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:04:59.937 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:05:40.047 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:06:20.133 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:07:00.239 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:07:40.347 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:08:20.441 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:09:00.539 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:09:40.639 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:10:20.741 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:11:00.840 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:11:40.939 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:12:21.040 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:13:01.140 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:13:41.244 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:14:21.343 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:15:01.439 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:15:41.545 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:16:21.646 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:17:01.741 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:17:41.844 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:18:21.942 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:19:02.049 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:19:42.118 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:20:22.216 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:21:02.321 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:21:42.420 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:22:22.520 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:23:02.616 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:23:42.721 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:24:22.818 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:25:02.918 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:25:43.030 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:26:23.117 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:27:03.225 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:27:43.321 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:28:23.418 [lettuce-eventExecutorLoop-1-9] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 01:29:03.519 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was *************:6380
2025-08-06 09:10:18.515 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-06 09:10:18.820 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Starting AuthServerApplication using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 11444 (/Users/<USER>/WorkSpace/git-sanyth-sso/git-sanyth-sso-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/git-sanyth-sso)
2025-08-06 09:10:18.821 [main] INFO  com.sanyth.auth.server.AuthServerApplication - The following 1 profile is active: "dev"
2025-08-06 09:10:20.494 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-06 09:10:20.498 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data LDAP repositories in DEFAULT mode.
2025-08-06 09:10:20.753 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 230 ms. Found 2 LDAP repository interfaces.
2025-08-06 09:10:20.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-06 09:10:20.797 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-06 09:10:20.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-06 09:10:20.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository.
2025-08-06 09:10:20.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 MongoDB repository interfaces.
2025-08-06 09:10:20.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-06 09:10:20.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-06 09:10:20.857 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.OuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-06 09:10:20.858 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.auth.server.ldap.repository.PersonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-06 09:10:20.858 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-08-06 09:10:21.762 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:10:21.780 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:10:21.805 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-06 09:10:22.422 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8090 (http)
2025-08-06 09:10:22.461 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8090"]
2025-08-06 09:10:22.462 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-06 09:10:22.462 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-06 09:10:22.620 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-06 09:10:22.621 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3715 ms
2025-08-06 09:10:23.462 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-06 09:10:23.463 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-06 09:10:23.465 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-06 09:10:25.813 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-06 09:10:27.308 [cluster-rtt-ClusterId{value='6892ab81667b7b7f085b2a79', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21467}] to *************:13337
2025-08-06 09:10:27.308 [cluster-ClusterId{value='6892ab81667b7b7f085b2a79', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21466}] to *************:13337
2025-08-06 09:10:27.310 [cluster-ClusterId{value='6892ab81667b7b7f085b2a79', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=180142875}
2025-08-06 09:10:29.547 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-06 09:10:29.788 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-06 09:10:29.808 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail'], Ant [pattern='/api/user-org/**']]
2025-08-06 09:10:29.842 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-06 09:10:37.435 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-06 09:10:37.507 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-06 09:10:37.509 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-06 09:10:37.511 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-06 09:10:37.780 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-06 09:10:37.803 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-06 09:10:37.809 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-06 09:10:37.809 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-06 09:10:37.810 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-06 09:10:37.810 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-06 09:10:37.810 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-06 09:10:37.810 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-06 09:10:37.811 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-06 09:10:37.817 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-06 09:10:37.817 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-06 09:10:37.939 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8090"]
2025-08-06 09:10:37.981 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8090 (http) with context path ''
2025-08-06 09:10:38.583 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-08-06 09:10:38.609 [main] INFO  com.sanyth.auth.server.AuthServerApplication - Started AuthServerApplication in 20.888 seconds (JVM running for 30.947)
2025-08-06 09:10:38.629 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-06 09:10:39.894 [main] INFO  com.zaxxer.hikari.pool.PoolBase - DatebookHikariCP - Driver does not support get/set network timeout for connections. (oracle.jdbc.driver.T4CConnection.getNetworkTimeout()I)
2025-08-06 09:10:40.289 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-06 09:11:07.621 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：datacenter
2025-08-06 09:13:17.382 [http-nio-8090-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-06 09:13:17.383 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-06 09:13:17.387 [http-nio-8090-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-06 09:13:18.365 [http-nio-8090-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:21472}] to *************:13337
2025-08-06 09:13:20.168 [http-nio-8090-exec-4] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:21473}] to *************:13337
2025-08-06 09:13:29.481 [http-nio-8090-exec-8] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:21474}] to *************:13337
2025-08-06 09:13:29.486 [http-nio-8090-exec-6] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:21477}] to *************:13337
2025-08-06 09:13:29.486 [http-nio-8090-exec-9] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:21476}] to *************:13337
2025-08-06 09:13:29.501 [http-nio-8090-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:21475}] to *************:13337
2025-08-06 09:14:59.589 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-06 09:15:10.145 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:15:19.145 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:25:19.468 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:27:08.602 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:31:42.499 [http-nio-8090-exec-6] INFO  org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint - Handling ClientRegistrationException error: No client with requested id: 067aw5R9klD0923E
2025-08-06 09:33:12.870 [http-nio-8090-exec-7] INFO  org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint - Handling ClientRegistrationException error: No client with requested id: du7Z21Ej3ts60SE4
2025-08-06 09:33:42.484 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:34:12.888 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:34:24.083 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:35:13.488 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:35:52.706 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:36:12.436 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:36:33.186 [http-nio-8090-exec-10] INFO  org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint - Handling ClientRegistrationException error: No client with requested id: du7Z21Ej3ts60SE4
2025-08-06 09:38:43.191 [http-nio-8090-exec-2] INFO  org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint - Handling ClientRegistrationException error: No client with requested id: du7Z21Ej3ts60SE4
2025-08-06 09:38:45.237 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:23.630 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:23.695 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:23.695 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:23.763 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:23.821 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:34.331 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:43.573 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:43.632 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:43.702 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:43.715 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:43.899 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:53.451 [http-nio-8090-exec-7] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Wed Aug 06 09:39:53 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-06 09:39:53.451 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Wed Aug 06 09:39:53 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-06 09:39:53.451 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Wed Aug 06 09:39:53 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-06 09:39:53.461 [http-nio-8090-exec-10] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Wed Aug 06 09:39:53 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-06 09:39:53.470 [http-nio-8090-exec-4] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=系统管理, subType=管理视图, bizNo={role=管理员帐号}, operator=sytadmin, action=切换角色, fail=false, createTime=Wed Aug 06 09:39:53 CST 2025, extra=, codeVariable={MethodName=switchRole, ClassName=class com.sanyth.auth.server.web.UserController})
2025-08-06 09:39:55.718 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:55.795 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:55.865 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:39:55.926 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:02.992 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:03.114 [http-nio-8090-exec-6] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:03.171 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:03.297 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:03.982 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:04.187 [http-nio-8090-exec-11] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:21487}] to *************:13337
2025-08-06 09:40:04.188 [http-nio-8090-exec-12] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:21488}] to *************:13337
2025-08-06 09:40:04.188 [http-nio-8090-exec-5] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:04.593 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:04.661 [http-nio-8090-exec-14] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:04.661 [http-nio-8090-exec-15] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:04.731 [http-nio-8090-exec-8] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:04.801 [http-nio-8090-exec-15] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:04.803 [http-nio-8090-exec-14] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:13.285 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:13.416 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:13.814 [http-nio-8090-exec-2] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@68382f45, operator=sytadmin, action=查询认证应用, fail=false, createTime=Wed Aug 06 09:40:13 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-06 09:40:14.911 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:15.040 [http-nio-8090-exec-3] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:33.252 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:33.385 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.RoleResourceServiceImpl - 缓存获取权限
2025-08-06 09:40:33.590 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.service.impl.DbLogRecordServiceImpl - 【logRecord】log=LogRecord(id=null, tenant=com.sanyth.auth, type=认证应用管理, subType=管理视图, bizNo=com.sanyth.auth.server.model.OauthClientDetails@73a2e101, operator=sytadmin, action=查询认证应用, fail=false, createTime=Wed Aug 06 09:40:33 CST 2025, extra=, codeVariable={MethodName=queryPage, ClassName=class com.sanyth.auth.server.web.OauthClientDetailsController})
2025-08-06 09:41:15.289 [http-nio-8090-exec-1] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-06 09:41:15.407 [http-nio-8090-exec-9] INFO  com.sanyth.auth.server.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-06 09:41:36.322 [http-nio-8090-exec-14] INFO  org.springframework.security.oauth2.provider.token.store.JdbcTokenStore - Failed to find access token for token a6864019-e176-4653-8f67-19b25a91b17e
2025-08-06 09:44:23.585 [cluster-rtt-ClusterId{value='6892ab81667b7b7f085b2a79', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:11, serverValue:21489}] to *************:13337
