package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.mapper.SytSysOrganizationMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.service.impl.ThirdPartyApiServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 第三方API服务测试类
 * @since 2025-01-08
 */
@ExtendWith(MockitoExtension.class)
public class ThirdPartyApiServiceTest {

    @Mock
    private SytPermissionAccountMapper sytPermissionAccountMapper;

    @Mock
    private SytSysOrganizationMapper sytSysOrganizationMapper;

    @InjectMocks
    private ThirdPartyApiServiceImpl thirdPartyApiService;

    private SytPermissionAccount mockAccount;
    private SytSysOrganization mockOrganization;

    @BeforeEach
    void setUp() {
        // 创建模拟用户数据
        mockAccount = new SytPermissionAccount();
        mockAccount.setId("user123");
        mockAccount.setHumancode("testuser");
        mockAccount.setHumanname("测试用户");
        mockAccount.setEmail("<EMAIL>");
        mockAccount.setTelmobile1("***********");
        mockAccount.setCreatedate(new Date());
        mockAccount.setValidflag(1.0);
        mockAccount.setSex("男");

        // 创建模拟组织机构数据
        mockOrganization = new SytSysOrganization();
        mockOrganization.setId("org123");
        mockOrganization.setCode("TECH");
        mockOrganization.setOrgname("技术部");
        mockOrganization.setOrgshortname("技术部");
        mockOrganization.setValid("1");
        mockOrganization.setDisplayorder(1.0);
    }

    @Test
    void testGetUserInfoById() {
        // 准备测试数据
        when(sytPermissionAccountMapper.selectById("user123")).thenReturn(mockAccount);
        when(sytSysOrganizationMapper.getOrganizationByUser("user123")).thenReturn(new ArrayList<>());

        // 执行测试
        UserInfoDTO result = thirdPartyApiService.getUserInfoById("user123");

        // 验证结果
        assertNotNull(result);
        assertEquals("user123", result.getId());
        assertEquals("testuser", result.getHumancode());
        assertEquals("测试用户", result.getHumanname());
        assertEquals("<EMAIL>", result.getEmail());
        assertEquals("***********", result.getTelmobile1());

        // 验证方法调用
        verify(sytPermissionAccountMapper, times(1)).selectById("user123");
        verify(sytSysOrganizationMapper, times(1)).getOrganizationByUser("user123");
    }

    @Test
    void testGetUserInfoByIdNotFound() {
        // 准备测试数据
        when(sytPermissionAccountMapper.selectById("nonexistent")).thenReturn(null);

        // 执行测试
        UserInfoDTO result = thirdPartyApiService.getUserInfoById("nonexistent");

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(sytPermissionAccountMapper, times(1)).selectById("nonexistent");
        verify(sytSysOrganizationMapper, never()).getOrganizationByUser(anyString());
    }

    @Test
    void testGetOrganizationInfoById() {
        // 准备测试数据
        when(sytSysOrganizationMapper.selectById("org123")).thenReturn(mockOrganization);

        // 执行测试
        OrganizationInfoDTO result = thirdPartyApiService.getOrganizationInfoById("org123");

        // 验证结果
        assertNotNull(result);
        assertEquals("org123", result.getId());
        assertEquals("TECH", result.getCode());
        assertEquals("技术部", result.getOrgname());
        assertEquals("技术部", result.getOrgshortname());
        assertEquals("1", result.getValid());

        // 验证方法调用
        verify(sytSysOrganizationMapper, times(1)).selectById("org123");
    }

    @Test
    void testGetOrganizationInfoByIdNotFound() {
        // 准备测试数据
        when(sytSysOrganizationMapper.selectById("nonexistent")).thenReturn(null);

        // 执行测试
        OrganizationInfoDTO result = thirdPartyApiService.getOrganizationInfoById("nonexistent");

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(sytSysOrganizationMapper, times(1)).selectById("nonexistent");
    }

    @Test
    void testGetOrganizationsByUserId() {
        // 准备测试数据
        List<SytSysOrganization> orgList = new ArrayList<>();
        orgList.add(mockOrganization);
        when(sytSysOrganizationMapper.getOrganizationByUser("user123")).thenReturn(orgList);

        // 执行测试
        List<OrganizationInfoDTO> result = thirdPartyApiService.getOrganizationsByUserId("user123");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("org123", result.get(0).getId());
        assertEquals("技术部", result.get(0).getOrgname());

        // 验证方法调用
        verify(sytSysOrganizationMapper, times(1)).getOrganizationByUser("user123");
    }

    @Test
    void testGetOrganizationsByUserIdEmpty() {
        // 准备测试数据
        when(sytSysOrganizationMapper.getOrganizationByUser("user123")).thenReturn(new ArrayList<>());

        // 执行测试
        List<OrganizationInfoDTO> result = thirdPartyApiService.getOrganizationsByUserId("user123");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(sytSysOrganizationMapper, times(1)).getOrganizationByUser("user123");
    }

    @Test
    void testGetUserInfoByIdWithBlankId() {
        // 执行测试
        UserInfoDTO result1 = thirdPartyApiService.getUserInfoById("");
        UserInfoDTO result2 = thirdPartyApiService.getUserInfoById(null);

        // 验证结果
        assertNull(result1);
        assertNull(result2);

        // 验证方法调用
        verify(sytPermissionAccountMapper, never()).selectById(anyString());
    }

    @Test
    void testGetOrganizationInfoByIdWithBlankId() {
        // 执行测试
        OrganizationInfoDTO result1 = thirdPartyApiService.getOrganizationInfoById("");
        OrganizationInfoDTO result2 = thirdPartyApiService.getOrganizationInfoById(null);

        // 验证结果
        assertNull(result1);
        assertNull(result2);

        // 验证方法调用
        verify(sytSysOrganizationMapper, never()).selectById(anyString());
    }

    @Test
    void testGetOrganizationsByUserIdWithBlankId() {
        // 执行测试
        List<OrganizationInfoDTO> result1 = thirdPartyApiService.getOrganizationsByUserId("");
        List<OrganizationInfoDTO> result2 = thirdPartyApiService.getOrganizationsByUserId(null);

        // 验证结果
        assertNotNull(result1);
        assertTrue(result1.isEmpty());
        assertNotNull(result2);
        assertTrue(result2.isEmpty());

        // 验证方法调用
        verify(sytSysOrganizationMapper, never()).getOrganizationByUser(anyString());
    }
}
