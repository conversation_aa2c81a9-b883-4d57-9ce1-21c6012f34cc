package com.sanyth.sso.client.validation;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.sanyth.sso.client.bean.ClientDetails;
import com.sanyth.sso.client.bean.UserDetails;
import com.sanyth.sso.client.http.HttpClient;
import com.sanyth.sso.client.http.SytClientHttpServletRequestWrapper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.Map;
import java.util.Random;

public class AuthenticationFilter implements Filter {

    protected ClientDetails clientDetails;
    protected static String POST = "POST";
    protected static String redirectCallbackUrl = "/sso/client/login";
    protected static String notCheckUrlRegex;
    protected static final String sytAuthUsername = "_currentUsername";

    public AuthenticationFilter() {

    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        initConfiguration(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        try {
            GsonBuilder gb = new GsonBuilder();
            Gson g = gb.create();
            String ticket = request.getParameter("syt_ticket");
            if (!isEmpty(ticket)) {
                String params = "syt_ticket=" + ticket;
                String result = null;
                try {
                    result = HttpClient.createRequest(clientDetails.getServiceValidateUri(), POST, params);
                    System.out.println("getServiceValidateUri:" + result);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                JsonObject jsonObject = (JsonObject) new JsonParser().parse(result);
                if ("00001".equals(jsonObject.get("code").getAsString())) {
                    throw new ServletException(jsonObject.get("info").getAsString());
                }
                JsonObject info = jsonObject.getAsJsonObject("info");
                UserDetails userDetails = new Gson().fromJson(info, UserDetails.class);
                authorizedSuccess(userDetails, request, response);
                filterChain.doFilter(newRequestWrapper(request, userDetails.getName()), response);
            } else {
                String access_token = request.getParameter("access_token");
                access_token = isEmpty(access_token) ? request.getHeader("access_token") : access_token;
                if (!isEmpty(access_token)) {
                    getUserByToken(access_token, filterChain, request, response);
                } else {
                    String requestURI = request.getRequestURI();
                    if (!isEmpty(notCheckUrlRegex) && requestURI.matches(notCheckUrlRegex)) {
                        filterChain.doFilter(request, response);
                    } else {
                        if (checkIsAuthorized(request, response)) {
                            filterChain.doFilter(request, response);
                        } else {
                            String code = request.getParameter("code");
                            String state = request.getParameter("state");
                            if (/*requestURI.endsWith(redirectCallbackUrl) && */!isEmpty(code)) {
                                String cacheState = cacheState(request);
                                if (!isEmpty(cacheState)) {
                                    if (isEmpty(state)) {
                                        out(response, 401, "Unauthorized");
                                        return;
                                    }

                                    if (!cacheState.equals(state)) {
                                        out(response, 401, "Unauthorized");
                                        return;
                                    }
                                    removeState(request);
                                }

                                String params = "grant_type=authorization_code&client_id=" + clientDetails.getClientId() + "&client_secret=" + clientDetails.getClientSecret() + "&code=" + code;
                                String result = null;
                                try {
                                    result = HttpClient.createRequest(clientDetails.getAccessTokenUri(), POST, params);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    out(response, 500, e.getMessage());
                                }
                                if (!isEmpty(result)) {
                                    Map<String, String> resultMap = g.fromJson(result, new TypeToken<Map<String, String>>() {
                                    }.getType());

                                    getUserByToken(resultMap.get("access_token"), filterChain, request, response);
                                } else {
                                    out(response, 500, "未获取到access_token信息");
                                }
                            } else {
                                String randomState = randomStateCode();
                                cacheState(randomState, request);
                                String params = "client_id=" + clientDetails.getClientId() + "&response_type=code&state=" + randomState;
                                response.sendRedirect(clientDetails.getUserAuthorizationUri() + "?" + params);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    protected void getUserByToken(String access_token, FilterChain filterChain,
                                  HttpServletRequest request, HttpServletResponse response) throws IOException, ServletException {
        String params = "access_token=" + access_token;
        String result = null;
        try {
            result = HttpClient.createRequest(clientDetails.getUserInfoUri(), POST, params);
        } catch (Exception e) {
            out(response, 500, e.getMessage());
        }
        UserDetails userDetails = new Gson().fromJson(result, UserDetails.class);
        authorizedSuccess(userDetails, request, response);
        filterChain.doFilter(newRequestWrapper(request, userDetails.getName()), response);
    }

    private SytClientHttpServletRequestWrapper newRequestWrapper(HttpServletRequest request, String remoteUser) {
        return new SytClientHttpServletRequestWrapper(request, remoteUser);
    }

    protected boolean checkIsAuthorized(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        if (session.getAttribute(sytAuthUsername) != null) {
            return true;
        }
        return false;
    }

    protected void authorizedSuccess(UserDetails userDetails, HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        String currentUsername = userDetails.getUsername();
        System.out.println("=== SSO AuthenticationFilter Debug ===");
        System.out.println("authorizedSuccess:" + currentUsername);
        System.out.println("Session ID: " + session.getId());

        // 设置session
        session.setAttribute("_currentUsername", currentUsername);

        // 同时设置Cookie作为备用方案
        try {
            javax.servlet.http.Cookie cookie = new javax.servlet.http.Cookie("sso_temp_user", currentUsername);
            cookie.setMaxAge(300); // 5分钟有效期，足够完成SSO流程
            cookie.setPath("/"); // 设置为根路径，确保整个应用都能访问
            cookie.setHttpOnly(false); // 允许JavaScript访问（如果需要的话）
            response.addCookie(cookie);
            System.out.println("Set SSO cookie: sso_temp_user = " + currentUsername);
        } catch (Exception e) {
            System.out.println("Failed to set SSO cookie: " + e.getMessage());
        }

        String requestURI = request.getRequestURI();
        if (requestURI.contains("oauth.jsp")) {
            try {
                String encodedUsername = URLEncoder.encode(currentUsername, "UTF-8");
                String redirectUrl = requestURI + "?username=" + encodedUsername + "&timestamp=" + System.currentTimeMillis();
                System.out.println("Redirecting to: " + redirectUrl);
                response.sendRedirect(redirectUrl);
                return;
            } catch (Exception e) {
                System.out.println("URL redirect failed: " + e.getMessage());
            }
        }

        System.out.println("authorizedSuccess completed for: " + currentUsername);
    }

    protected void authorizedFailed(Exception e, HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

    }

    protected void cacheState(String state, HttpServletRequest request) throws ServletException, IOException {

    }

    protected String cacheState(HttpServletRequest request) throws ServletException, IOException {
        return null;
    }

    protected void removeState(HttpServletRequest request) throws ServletException, IOException {

    }

    @Override
    public void destroy() {

    }

    protected void out(HttpServletResponse response, int status,
                       String retString) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setContentType("text/json;charset=UTF-8");
        response.setStatus(status);
        PrintWriter out = response.getWriter();
        out.print("{\"code\":\"" + status + "\", \"info\":\"" + retString + "\"}");
        out.flush();
        out.close();
    }

    protected String randomStateCode() {
        int len = new Random().nextInt(10 + 6);
        StringBuilder str = new StringBuilder();
        String chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        char[] charArray = chars.toCharArray();
        for (int j = 0; j < len; j++) {
            char c = charArray[(int) (Math.random() * 52)];
            str.append(c);
        }
        return str.toString();
    }

    protected String extractToken(HttpServletRequest request) {
        String token = this.extractHeaderToken(request);
        if (token == null) {
//            logger.debug("Token not found in headers. Trying request parameters.");
            token = request.getParameter("access_token");
            if (token == null) {
//                logger.debug("Token not found in request parameters.  Not an OAuth2 request.");
            } else {
                request.setAttribute("OAuth2AuthenticationDetails.ACCESS_TOKEN_TYPE", "Bearer");
            }
        }
        return token;
    }

    protected String extractHeaderToken(HttpServletRequest request) {
        Enumeration headers = request.getHeaders("Authorization");
        String value;
        do {
            if (!headers.hasMoreElements()) {
                return null;
            }
            value = (String) headers.nextElement();
        } while (!value.toLowerCase().startsWith("Bearer".toLowerCase()));
        String authHeaderValue = value.substring("Bearer".length()).trim();
        request.setAttribute("OAuth2AuthenticationDetails.ACCESS_TOKEN_TYPE", value.substring(0, "Bearer".length()).trim());
        int commaIndex = authHeaderValue.indexOf(44);
        if (commaIndex > 0) {
            authHeaderValue = authHeaderValue.substring(0, commaIndex);
        }
        return authHeaderValue;
    }

    private static String replacePostfix(String serverName) {
        if (serverName.endsWith("/")) {
            return replacePostfix(serverName.substring(0, serverName.length() - 1));
        }
        return serverName;
    }

    public void initConfiguration(FilterConfig config) throws ServletException {
        clientDetails = new ClientDetails();
        clientDetails.setClientId(config.getInitParameter("clientId"));
        clientDetails.setClientSecret(config.getInitParameter("clientSecret"));
        String serverName = config.getInitParameter("serverName");
        if (isEmpty(clientDetails.getClientId()))
            throw new ServletException("clientId is required.");
        if (isEmpty(clientDetails.getClientSecret()))
            throw new ServletException("clientSecret is required.");
        if (isEmpty(serverName))
            throw new ServletException("serverName is required.");
        serverName = replacePostfix(serverName);
        clientDetails.setAccessTokenUri(serverName + "/oauth/token");
        clientDetails.setUserAuthorizationUri(serverName + "/oauth/authorize");
        clientDetails.setUserInfoUri(serverName + "/user/me");
        clientDetails.setServiceValidateUri(serverName + "/serviceValidate");
        notCheckUrlRegex = config.getInitParameter("notCheckUrlRegex");
    }

    protected static boolean isEmpty(Object param) {
        if (param == null || "".equals(param)) {
            return true;
        }
        return false;
    }
}
