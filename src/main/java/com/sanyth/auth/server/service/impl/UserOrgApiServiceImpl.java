package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.mapper.SytSysOrganizationMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.service.IUserOrgApiService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户组织机构API服务实现
 * @since 2025-08-06
 */
@Service
public class UserOrgApiServiceImpl extends ServiceImpl<SytPermissionAccountMapper, SytPermissionAccount> implements IUserOrgApiService {

    @Resource
    private SytPermissionAccountMapper sytPermissionAccountMapper;

    @Resource
    private SytSysOrganizationMapper sytSysOrganizationMapper;

    @Override
    public Page<UserInfoDTO> queryPage(BaseQuery<UserInfoDTO> query) {
        Page<SytPermissionAccount> page = new Page<>(query.getPage(), query.getPageSize());
        UserInfoDTO obj = query.getQueryParam();
        
        // 构建查询条件
        Map<String, Object> paramMap = new HashMap<>();
        if (obj != null) {
            if (StringUtils.isNotBlank(obj.getHumancode())) {
                paramMap.put("humancode", obj.getHumancode());
            }
            if (StringUtils.isNotBlank(obj.getHumanname())) {
                paramMap.put("humanname", obj.getHumanname());
            }
            if (StringUtils.isNotBlank(obj.getOrgid())) {
                paramMap.put("organizationnames", obj.getOrgid());
            }
            if (StringUtils.isNotBlank(obj.getSex())) {
                paramMap.put("sex", obj.getSex());
            }
            if (obj.getValidflag() != null) {
                paramMap.put("validflag", obj.getValidflag());
            }
            if (StringUtils.isNotBlank(obj.getEmployeeType())) {
                paramMap.put("employeeType", obj.getEmployeeType());
            }
        }
        
        // 查询用户信息
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(paramMap, page);
        
        // 转换为DTO
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                // 获取用户所属组织机构
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }
        
        // 构建分页结果
        Page<UserInfoDTO> resultPage = new Page<>(query.getPage(), query.getPageSize());
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(page.getTotal());
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setPages(page.getPages());
        
        return resultPage;
    }

    @Override
    public List<UserInfoDTO> queryList(UserInfoDTO userInfoDTO) {
        // 构建查询条件
        Map<String, Object> paramMap = new HashMap<>();
        if (userInfoDTO != null) {
            if (StringUtils.isNotBlank(userInfoDTO.getHumancode())) {
                paramMap.put("humancode", userInfoDTO.getHumancode());
            }
            if (StringUtils.isNotBlank(userInfoDTO.getHumanname())) {
                paramMap.put("humanname", userInfoDTO.getHumanname());
            }
            if (StringUtils.isNotBlank(userInfoDTO.getOrgid())) {
                paramMap.put("organizationnames", userInfoDTO.getOrgid());
            }
        }
        
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(paramMap, null);
        
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO dto = convertToUserInfoDTO(account);
                // 获取用户所属组织机构
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                dto.setOrganizations(organizations);
                userInfoDTOList.add(dto);
            }
        }
        
        return userInfoDTOList;
    }

    @Override
    public Page<OrganizationInfoDTO> queryOrgPage(BaseQuery<OrganizationInfoDTO> query) {
        Page<SytSysOrganization> page = new Page<>(query.getPage(), query.getPageSize());
        OrganizationInfoDTO obj = query.getQueryParam();
        Wrapper<SytSysOrganization> wrapper = buildOrgWrapper(obj);
        Page<SytSysOrganization> resultPage = sytSysOrganizationMapper.selectPage(page, wrapper);
        
        // 转换为DTO
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            for (SytSysOrganization org : resultPage.getRecords()) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        // 构建分页结果
        Page<OrganizationInfoDTO> dtoPage = new Page<>(query.getPage(), query.getPageSize());
        dtoPage.setRecords(orgInfoDTOList);
        dtoPage.setTotal(resultPage.getTotal());
        dtoPage.setCurrent(resultPage.getCurrent());
        dtoPage.setSize(resultPage.getSize());
        dtoPage.setPages(resultPage.getPages());
        
        return dtoPage;
    }

    @Override
    public List<OrganizationInfoDTO> queryOrgList(OrganizationInfoDTO organizationInfoDTO) {
        Wrapper<SytSysOrganization> wrapper = buildOrgWrapper(organizationInfoDTO);
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.selectList(wrapper);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    @Override
    public List<OrganizationInfoDTO> treeList() {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        wrapper.and(w -> w.isNull("PARENT").or().eq("PARENT", ""));
        wrapper.orderByAsc("DISPLAYORDER");
        
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.selectList(wrapper);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                
                // 递归获取子机构
                List<OrganizationInfoDTO> children = getChildrenOrganizations(org.getId());
                if (!CollectionUtils.isEmpty(children)) {
                    orgInfoDTO.setChildren(children);
                }
                
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    @Override
    public List<OrganizationInfoDTO> getOrganizationsByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new ArrayList<>();
        }
        
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.getOrganizationByUser(userId);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    @Override
    public Page<UserInfoDTO> getUsersByOrganizationId(BaseQuery<UserInfoDTO> query) {
        Page<SytPermissionAccount> page = new Page<>(query.getPage(), query.getPageSize());
        UserInfoDTO obj = query.getQueryParam();
        
        // 构建查询条件
        Map<String, Object> paramMap = new HashMap<>();
        if (obj != null && StringUtils.isNotBlank(obj.getOrgid())) {
            paramMap.put("organizationnames", obj.getOrgid());
        }
        
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(paramMap, page);
        
        // 转换为DTO
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                // 获取用户所属组织机构
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }
        
        // 构建分页结果
        Page<UserInfoDTO> resultPage = new Page<>(query.getPage(), query.getPageSize());
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(page.getTotal());
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setPages(page.getPages());
        
        return resultPage;
    }

    /**
     * 构建组织机构查询条件
     */
    private Wrapper<SytSysOrganization> buildOrgWrapper(OrganizationInfoDTO organizationInfoDTO) {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if (organizationInfoDTO != null) {
            if (StringUtils.isNotBlank(organizationInfoDTO.getOrgname())) {
                wrapper.like("ORGNAME", organizationInfoDTO.getOrgname());
            }
            if (StringUtils.isNotBlank(organizationInfoDTO.getParent())) {
                wrapper.eq("PARENT", organizationInfoDTO.getParent());
            }
            if (StringUtils.isNotBlank(organizationInfoDTO.getCategoryId())) {
                wrapper.eq("CATEGORY_ID", organizationInfoDTO.getCategoryId());
            }
            if (StringUtils.isNotBlank(organizationInfoDTO.getValid())) {
                wrapper.eq("VALID", organizationInfoDTO.getValid());
            }
            if (StringUtils.isNotBlank(organizationInfoDTO.getCode())) {
                wrapper.like("CODE", organizationInfoDTO.getCode());
            }
        }
        wrapper.orderByAsc("DISPLAYORDER");
        return wrapper;
    }

    /**
     * 递归获取子机构
     */
    private List<OrganizationInfoDTO> getChildrenOrganizations(String parentId) {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        wrapper.eq("PARENT", parentId);
        wrapper.orderByAsc("DISPLAYORDER");
        
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.selectList(wrapper);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                
                // 递归获取子机构
                List<OrganizationInfoDTO> children = getChildrenOrganizations(org.getId());
                if (!CollectionUtils.isEmpty(children)) {
                    orgInfoDTO.setChildren(children);
                }
                
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    /**
     * 将SytPermissionAccount转换为UserInfoDTO
     */
    private UserInfoDTO convertToUserInfoDTO(SytPermissionAccount account) {
        if (account == null) {
            return null;
        }
        
        UserInfoDTO dto = new UserInfoDTO();
        BeanUtils.copyProperties(account, dto);
        
        return dto;
    }

    /**
     * 将SytSysOrganization转换为OrganizationInfoDTO
     */
    private OrganizationInfoDTO convertToOrganizationInfoDTO(SytSysOrganization organization) {
        if (organization == null) {
            return null;
        }
        
        OrganizationInfoDTO dto = new OrganizationInfoDTO();
        BeanUtils.copyProperties(organization, dto);
        
        return dto;
    }
}
