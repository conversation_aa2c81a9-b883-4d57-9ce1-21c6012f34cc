package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.OrganizationQueryDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.dto.UserQueryDTO;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.mapper.SytSysOrganizationMapper;
import com.sanyth.auth.server.mapper.SytSysOrganizationUserMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.service.IUserOrgApiService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 第三方API服务实现类
 * @since 2025-08-05
 */
@Service
public class UserOrgApiServiceImpl implements IUserOrgApiService {

    @Resource
    private SytPermissionAccountMapper sytPermissionAccountMapper;

    @Resource
    private SytSysOrganizationMapper sytSysOrganizationMapper;

    @Resource
    private SytSysOrganizationUserMapper sytSysOrganizationUserMapper;

    @Override
    public Page<UserInfoDTO> getUserInfoPage(UserQueryDTO queryDTO) {
        Page<SytPermissionAccount> accountPage = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        // 构建查询条件
        Map<String, Object> queryParam = new HashMap<>();
        if (StringUtils.isNotBlank(queryDTO.getHumancode())) {
            queryParam.put("humancode", queryDTO.getHumancode());
        }
        if (StringUtils.isNotBlank(queryDTO.getHumanname())) {
            queryParam.put("humanname", queryDTO.getHumanname());
        }
        if (StringUtils.isNotBlank(queryDTO.getOrgId())) {
            queryParam.put("organizationnames", queryDTO.getOrgId());
        }
        if (StringUtils.isNotBlank(queryDTO.getSex())) {
            queryParam.put("sex", queryDTO.getSex());
        }
        if (queryDTO.getValidflag() != null) {
            queryParam.put("validflag", queryDTO.getValidflag());
        }
        if (StringUtils.isNotBlank(queryDTO.getEmployeeType())) {
            queryParam.put("employeeType", queryDTO.getEmployeeType());
        }
        if (StringUtils.isNotBlank(queryDTO.getCurrentState())) {
            queryParam.put("currentState", queryDTO.getCurrentState());
        }

        // 查询用户信息
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(queryParam, accountPage);

        // 转换为DTO
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                // 获取用户所属组织机构
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }

        // 构建分页结果
        Page<UserInfoDTO> resultPage = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(accountPage.getTotal());
        resultPage.setCurrent(accountPage.getCurrent());
        resultPage.setSize(accountPage.getSize());
        resultPage.setPages(accountPage.getPages());

        return resultPage;
    }

    // 保持向后兼容的旧方法
    public Page<UserInfoDTO> getUserInfoPage(int page, int size, String humancode, String humanname, String orgId) {
        Page<SytPermissionAccount> accountPage = new Page<>(page, size);
        Map<String, Object> queryParam = new HashMap<>();
        if (StringUtils.isNotBlank(humancode)) {
            queryParam.put("humancode", humancode);
        }
        if (StringUtils.isNotBlank(humanname)) {
            queryParam.put("humanname", humanname);
        }
        if (StringUtils.isNotBlank(orgId)) {
            queryParam.put("organizationnames", orgId);
        }
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(queryParam, accountPage);
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }
        Page<UserInfoDTO> resultPage = new Page<>(page, size);
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(accountPage.getTotal());
        resultPage.setCurrent(accountPage.getCurrent());
        resultPage.setSize(accountPage.getSize());
        resultPage.setPages(accountPage.getPages());
        return resultPage;
    }

    @Override
    public UserInfoDTO getUserInfoById(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        SytPermissionAccount account = sytPermissionAccountMapper.selectById(userId);
        if (account == null) {
            return null;
        }
        UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
        List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(userId);
        userInfoDTO.setOrganizations(organizations);
        return userInfoDTO;
    }

    @Override
    public UserInfoDTO getUserInfoByHumancode(String humancode) {
        if (StringUtils.isBlank(humancode)) {
            return null;
        }
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("HUMANCODE", humancode);
        SytPermissionAccount account = sytPermissionAccountMapper.selectOne(wrapper);
        
        if (account == null) {
            return null;
        }
        
        UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
        List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
        userInfoDTO.setOrganizations(organizations);
        
        return userInfoDTO;
    }

    @Override
    public Page<OrganizationInfoDTO> getOrganizationInfoPage(int page, int size, String orgname, String parentId) {
        Page<SytSysOrganization> orgPage = new Page<>(page, size);
        
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(orgname)) {
            wrapper.like("ORGNAME", orgname);
        }
        if (StringUtils.isNotBlank(parentId)) {
            wrapper.eq("PARENT", parentId);
        }
        wrapper.orderByAsc("DISPLAYORDER");
        
        Page<SytSysOrganization> resultPage = sytSysOrganizationMapper.selectPage(orgPage, wrapper);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            for (SytSysOrganization org : resultPage.getRecords()) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        Page<OrganizationInfoDTO> dtoPage = new Page<>(page, size);
        dtoPage.setRecords(orgInfoDTOList);
        dtoPage.setTotal(resultPage.getTotal());
        dtoPage.setCurrent(resultPage.getCurrent());
        dtoPage.setSize(resultPage.getSize());
        dtoPage.setPages(resultPage.getPages());
        
        return dtoPage;
    }

    @Override
    public OrganizationInfoDTO getOrganizationInfoById(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return null;
        }
        
        SytSysOrganization organization = sytSysOrganizationMapper.selectById(orgId);
        if (organization == null) {
            return null;
        }
        
        return convertToOrganizationInfoDTO(organization);
    }

    @Override
    public List<OrganizationInfoDTO> getOrganizationTree(String parentId) {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(parentId)) {
            wrapper.eq("PARENT", parentId);
        } else {
            wrapper.and(w -> w.isNull("PARENT").or().eq("PARENT", ""));
        }
        wrapper.orderByAsc("DISPLAYORDER");
        
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.selectList(wrapper);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                
                // 递归获取子机构
                List<OrganizationInfoDTO> children = getOrganizationTree(org.getId());
                if (!CollectionUtils.isEmpty(children)) {
                    orgInfoDTO.setChildren(children);
                }
                
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    @Override
    public List<OrganizationInfoDTO> getOrganizationsByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new ArrayList<>();
        }
        
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.getOrganizationByUser(userId);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    @Override
    public Page<UserInfoDTO> getUsersByOrganizationId(String orgId, int page, int size) {
        if (StringUtils.isBlank(orgId)) {
            return new Page<>(page, size);
        }
        Page<SytPermissionAccount> accountPage = new Page<>(page, size);
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("organizationnames", orgId);
        
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(queryParam, accountPage);
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }
        Page<UserInfoDTO> resultPage = new Page<>(page, size);
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(accountPage.getTotal());
        resultPage.setCurrent(accountPage.getCurrent());
        resultPage.setSize(accountPage.getSize());
        resultPage.setPages(accountPage.getPages());
        
        return resultPage;
    }
    private UserInfoDTO convertToUserInfoDTO(SytPermissionAccount account) {
        if (account == null) {
            return null;
        }
        UserInfoDTO dto = new UserInfoDTO();
        BeanUtils.copyProperties(account, dto);
        return dto;
    }
    private OrganizationInfoDTO convertToOrganizationInfoDTO(SytSysOrganization organization) {
        if (organization == null) {
            return null;
        }
        
        OrganizationInfoDTO dto = new OrganizationInfoDTO();
        BeanUtils.copyProperties(organization, dto);
        
        return dto;
    }

    // ==================== 新增的优化方法 ====================

    @Override
    public UserInfoDTO getUserInfo(String idType, String idValue) {
        if (StringUtils.isBlank(idType) || StringUtils.isBlank(idValue)) {
            return null;
        }

        SytPermissionAccount account = null;

        if ("id".equalsIgnoreCase(idType)) {
            account = sytPermissionAccountMapper.selectById(idValue);
        } else if ("humancode".equalsIgnoreCase(idType)) {
            QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
            wrapper.eq("HUMANCODE", idValue);
            account = sytPermissionAccountMapper.selectOne(wrapper);
        } else {
            throw new IllegalArgumentException("不支持的查询类型: " + idType + "，支持的类型: id, humancode");
        }

        if (account == null) {
            return null;
        }

        UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
        // 获取用户所属组织机构
        List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
        userInfoDTO.setOrganizations(organizations);

        return userInfoDTO;
    }

    @Override
    public List<UserInfoDTO> getUserInfoBatch(String idType, List<String> idValues) {
        if (StringUtils.isBlank(idType) || CollectionUtils.isEmpty(idValues)) {
            return new ArrayList<>();
        }

        List<SytPermissionAccount> accountList = new ArrayList<>();

        if ("id".equalsIgnoreCase(idType)) {
            accountList = sytPermissionAccountMapper.selectBatchIds(idValues);
        } else if ("humancode".equalsIgnoreCase(idType)) {
            QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
            wrapper.in("HUMANCODE", idValues);
            accountList = sytPermissionAccountMapper.selectList(wrapper);
        } else {
            throw new IllegalArgumentException("不支持的查询类型: " + idType + "，支持的类型: id, humancode");
        }

        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                // 获取用户所属组织机构
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }

        return userInfoDTOList;
    }

    @Override
    public Page<OrganizationInfoDTO> getOrganizationInfoPage(OrganizationQueryDTO queryDTO) {
        Page<SytSysOrganization> orgPage = new Page<>(queryDTO.getPage(), queryDTO.getSize());

        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(queryDTO.getOrgname())) {
            wrapper.like("ORGNAME", queryDTO.getOrgname());
        }
        if (StringUtils.isNotBlank(queryDTO.getParentId())) {
            wrapper.eq("PARENT", queryDTO.getParentId());
        }
        if (StringUtils.isNotBlank(queryDTO.getCategoryId())) {
            wrapper.eq("CATEGORY_ID", queryDTO.getCategoryId());
        }
        if (StringUtils.isNotBlank(queryDTO.getValid())) {
            wrapper.eq("VALID", queryDTO.getValid());
        }
        if (StringUtils.isNotBlank(queryDTO.getCode())) {
            wrapper.like("CODE", queryDTO.getCode());
        }
        wrapper.orderByAsc("DISPLAYORDER");

        Page<SytSysOrganization> resultPage = sytSysOrganizationMapper.selectPage(orgPage, wrapper);

        // 转换为DTO
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            for (SytSysOrganization org : resultPage.getRecords()) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }

        // 构建分页结果
        Page<OrganizationInfoDTO> dtoPage = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        dtoPage.setRecords(orgInfoDTOList);
        dtoPage.setTotal(resultPage.getTotal());
        dtoPage.setCurrent(resultPage.getCurrent());
        dtoPage.setSize(resultPage.getSize());
        dtoPage.setPages(resultPage.getPages());

        return dtoPage;
    }

    @Override
    public List<OrganizationInfoDTO> getOrganizationInfoBatch(List<String> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return new ArrayList<>();
        }

        List<SytSysOrganization> orgList = sytSysOrganizationMapper.selectBatchIds(orgIds);

        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }

        return orgInfoDTOList;
    }

    @Override
    public List<OrganizationInfoDTO> getOrganizationTree(String parentId, boolean includeUsers) {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(parentId)) {
            wrapper.eq("PARENT", parentId);
        } else {
            wrapper.and(w -> w.isNull("PARENT").or().eq("PARENT", ""));
        }
        wrapper.orderByAsc("DISPLAYORDER");

        List<SytSysOrganization> orgList = sytSysOrganizationMapper.selectList(wrapper);

        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);

                // 递归获取子机构
                List<OrganizationInfoDTO> children = getOrganizationTree(org.getId(), includeUsers);
                if (!CollectionUtils.isEmpty(children)) {
                    orgInfoDTO.setChildren(children);
                }

                // 如果需要包含用户信息，可以在这里添加用户列表
                // 为了性能考虑，默认不加载用户信息

                orgInfoDTOList.add(orgInfoDTO);
            }
        }

        return orgInfoDTOList;
    }

    @Override
    public Page<UserInfoDTO> getUsersByOrganizationId(String orgId, int page, int size, boolean includeSubOrgs) {
        if (StringUtils.isBlank(orgId)) {
            return new Page<>(page, size);
        }

        Page<SytPermissionAccount> accountPage = new Page<>(page, size);

        // 构建查询条件
        Map<String, Object> queryParam = new HashMap<>();

        if (includeSubOrgs) {
            // 如果包含子机构，需要查询所有子机构的用户
            // 这里可以优化为一次查询获取所有子机构ID
            List<String> orgIds = getAllSubOrgIds(orgId);
            orgIds.add(orgId); // 包含当前机构
            queryParam.put("organizationnames", String.join(",", orgIds));
        } else {
            queryParam.put("organizationnames", orgId);
        }

        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(queryParam, accountPage);

        // 转换为DTO
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                // 获取用户所属组织机构
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }

        // 构建分页结果
        Page<UserInfoDTO> resultPage = new Page<>(page, size);
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(accountPage.getTotal());
        resultPage.setCurrent(accountPage.getCurrent());
        resultPage.setSize(accountPage.getSize());
        resultPage.setPages(accountPage.getPages());

        return resultPage;
    }

    /**
     * 获取所有子机构ID（递归）
     * @param parentId 父机构ID
     * @return 子机构ID列表
     */
    private List<String> getAllSubOrgIds(String parentId) {
        List<String> subOrgIds = new ArrayList<>();

        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        wrapper.eq("PARENT", parentId);
        List<SytSysOrganization> subOrgs = sytSysOrganizationMapper.selectList(wrapper);

        for (SytSysOrganization subOrg : subOrgs) {
            subOrgIds.add(subOrg.getId());
            // 递归获取子机构的子机构
            subOrgIds.addAll(getAllSubOrgIds(subOrg.getId()));
        }

        return subOrgIds;
    }
}
