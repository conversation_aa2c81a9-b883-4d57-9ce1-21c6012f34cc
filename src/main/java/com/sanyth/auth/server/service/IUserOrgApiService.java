package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.OrganizationQueryDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.dto.UserQueryDTO;

import java.util.List;

/**
 * 第三方API服务接口 - 优化版本
 * @since 2025-08-05
 */
public interface IUserOrgApiService {

    /**
     * 分页查询用户信息
     * @param queryDTO 查询参数对象
     * @return 用户信息分页结果
     */
    Page<UserInfoDTO> getUserInfoPage(UserQueryDTO queryDTO);

    /**
     * 分页查询用户信息（兼容旧版本）
     * @param page 页码
     * @param size 每页大小
     * @param humancode 用户代码（可选）
     * @param humanname 用户名称（可选）
     * @param orgId 组织机构ID（可选）
     * @return 用户信息分页结果
     */
    default Page<UserInfoDTO> getUserInfoPage(int page, int size, String humancode, String humanname, String orgId) {
        UserQueryDTO queryDTO = new UserQueryDTO();
        queryDTO.setPage(page);
        queryDTO.setSize(size);
        queryDTO.setHumancode(humancode);
        queryDTO.setHumanname(humanname);
        queryDTO.setOrgId(orgId);
        return getUserInfoPage(queryDTO);
    }

    /**
     * 根据条件获取单个用户信息（合并原来的两个方法）
     * @param idType 查询类型：id 或 humancode
     * @param idValue 查询值
     * @return 用户信息
     */
    UserInfoDTO getUserInfo(String idType, String idValue);

    /**
     * 批量获取用户信息
     * @param idType 查询类型：id 或 humancode
     * @param idValues 查询值列表
     * @return 用户信息列表
     */
    List<UserInfoDTO> getUserInfoBatch(String idType, List<String> idValues);

    /**
     * 分页查询组织机构信息（推荐使用）
     * @param queryDTO 查询参数对象
     * @return 组织机构信息分页结果
     */
    Page<OrganizationInfoDTO> getOrganizationInfoPage(OrganizationQueryDTO queryDTO);

    /**
     * 分页查询组织机构信息（兼容旧版本）
     * @param page 页码
     * @param size 每页大小
     * @param orgname 机构名称（可选）
     * @param parentId 上级机构ID（可选）
     * @return 组织机构信息分页结果
     */
    default Page<OrganizationInfoDTO> getOrganizationInfoPage(int page, int size, String orgname, String parentId) {
        OrganizationQueryDTO queryDTO = new OrganizationQueryDTO();
        queryDTO.setPage(page);
        queryDTO.setSize(size);
        queryDTO.setOrgname(orgname);
        queryDTO.setParentId(parentId);
        return getOrganizationInfoPage(queryDTO);
    }

    /**
     * 根据机构ID获取组织机构信息
     * @param orgId 机构ID
     * @return 组织机构信息
     */
    OrganizationInfoDTO getOrganizationInfoById(String orgId);

    /**
     * 批量获取组织机构信息
     * @param orgIds 机构ID列表
     * @return 组织机构信息列表
     */
    List<OrganizationInfoDTO> getOrganizationInfoBatch(List<String> orgIds);

    /**
     * 获取组织机构树形结构
     * @param parentId 父级机构ID，为null时获取根节点
     * @param includeUsers 是否包含用户信息
     * @return 组织机构树形结构
     */
    List<OrganizationInfoDTO> getOrganizationTree(String parentId, boolean includeUsers);

    /**
     * 根据用户ID获取用户所属的组织机构列表
     * @param userId 用户ID
     * @return 用户所属组织机构列表
     */
    List<OrganizationInfoDTO> getOrganizationsByUserId(String userId);

    /**
     * 根据组织机构ID获取该机构下的用户列表
     * @param orgId 组织机构ID
     * @param page 页码
     * @param size 每页大小
     * @param includeSubOrgs 是否包含子机构的用户
     * @return 用户信息分页结果
     */
    Page<UserInfoDTO> getUsersByOrganizationId(String orgId, int page, int size, boolean includeSubOrgs);

    // ==================== 便利方法 ====================

    /**
     * 根据用户ID获取用户信息（便利方法）
     * @param userId 用户ID
     * @return 用户信息
     */
    default UserInfoDTO getUserInfoById(String userId) {
        return getUserInfo("id", userId);
    }

    /**
     * 根据用户代码获取用户信息（便利方法）
     * @param humancode 用户代码
     * @return 用户信息
     */
    default UserInfoDTO getUserInfoByHumancode(String humancode) {
        return getUserInfo("humancode", humancode);
    }

    /**
     * 获取组织机构树形结构（便利方法，不包含用户信息）
     * @param parentId 父级机构ID
     * @return 组织机构树形结构
     */
    default List<OrganizationInfoDTO> getOrganizationTree(String parentId) {
        return getOrganizationTree(parentId, false);
    }

    /**
     * 根据组织机构ID获取该机构下的用户列表（便利方法，不包含子机构）
     * @param orgId 组织机构ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户信息分页结果
     */
    default Page<UserInfoDTO> getUsersByOrganizationId(String orgId, int page, int size) {
        return getUsersByOrganizationId(orgId, page, size, false);
    }
}
