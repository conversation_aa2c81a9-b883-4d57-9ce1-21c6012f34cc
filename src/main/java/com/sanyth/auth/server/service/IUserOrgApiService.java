package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.model.SytPermissionAccount;

import java.util.List;

/**
 * 用户组织机构API服务接口
 * @since 2025-08-06
 */
public interface IUserOrgApiService extends IService<SytPermissionAccount> {

    /**
     * 分页查询用户信息
     * @param query 查询参数
     * @return 用户信息分页结果
     */
    Page<UserInfoDTO> queryPage(BaseQuery<UserInfoDTO> query);

    /**
     * 查询用户信息列表
     * @param userInfoDTO 查询条件
     * @return 用户信息列表
     */
    List<UserInfoDTO> queryList(UserInfoDTO userInfoDTO);

    /**
     * 分页查询组织机构信息
     * @param query 查询参数
     * @return 组织机构信息分页结果
     */
    Page<OrganizationInfoDTO> queryOrgPage(BaseQuery<OrganizationInfoDTO> query);

    /**
     * 查询组织机构信息列表
     * @param organizationInfoDTO 查询条件
     * @return 组织机构信息列表
     */
    List<OrganizationInfoDTO> queryOrgList(OrganizationInfoDTO organizationInfoDTO);

    /**
     * 获取组织机构树形结构
     * @return 组织机构树形结构
     */
    List<OrganizationInfoDTO> treeList();

    /**
     * 根据用户ID获取用户所属的组织机构列表
     * @param userId 用户ID
     * @return 用户所属组织机构列表
     */
    List<OrganizationInfoDTO> getOrganizationsByUserId(String userId);

    /**
     * 根据组织机构ID获取该机构下的用户列表
     * @param query 查询参数
     * @return 用户信息分页结果
     */
    Page<UserInfoDTO> getUsersByOrganizationId(BaseQuery<UserInfoDTO> query);
}
