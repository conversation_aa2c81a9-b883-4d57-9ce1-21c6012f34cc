package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;

import java.util.List;

/**
 * 第三方API服务接口
 * @since 2025-08-05
 */
public interface IThirdPartyApiService {

    Page<UserInfoDTO> getUserInfoPage(int page, int size, String humancode, String humanname, String orgId);
    UserInfoDTO getUserInfoById(String userId);
    UserInfoDTO getUserInfoByHumancode(String humancode);
    Page<OrganizationInfoDTO> getOrganizationInfoPage(int page, int size, String orgname, String parentId);
    OrganizationInfoDTO getOrganizationInfoById(String orgId);
    List<OrganizationInfoDTO> getOrganizationTree(String parentId);
    List<OrganizationInfoDTO> getOrganizationsByUserId(String userId);
    Page<UserInfoDTO> getUsersByOrganizationId(String orgId, int page, int size);
}
