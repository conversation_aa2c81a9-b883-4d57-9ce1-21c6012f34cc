package com.sanyth.auth.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;

import java.util.List;

/**
 * 第三方API服务接口
 * @since 2025-08-05
 */
public interface IThirdPartyApiService {

    /**
     * 分页查询用户信息
     * @param page 页码
     * @param size 每页大小
     * @param humancode 用户代码（可选）
     * @param humanname 用户名称（可选）
     * @param orgId 组织机构ID（可选）
     * @return 用户信息分页结果
     */
    Page<UserInfoDTO> getUserInfoPage(int page, int size, String humancode, String humanname, String orgId);

    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoDTO getUserInfoById(String userId);

    /**
     * 根据用户代码获取用户信息
     * @param humancode 用户代码
     * @return 用户信息
     */
    UserInfoDTO getUserInfoByHumancode(String humancode);

    /**
     * 分页查询组织机构信息
     * @param page 页码
     * @param size 每页大小
     * @param orgname 机构名称（可选）
     * @param parentId 上级机构ID（可选）
     * @return 组织机构信息分页结果
     */
    Page<OrganizationInfoDTO> getOrganizationInfoPage(int page, int size, String orgname, String parentId);

    /**
     * 根据机构ID获取组织机构信息
     * @param orgId 机构ID
     * @return 组织机构信息
     */
    OrganizationInfoDTO getOrganizationInfoById(String orgId);

    /**
     * 获取组织机构树形结构
     * @param parentId 父级机构ID，为null时获取根节点
     * @return 组织机构树形结构
     */
    List<OrganizationInfoDTO> getOrganizationTree(String parentId);

    /**
     * 根据用户ID获取用户所属的组织机构列表
     * @param userId 用户ID
     * @return 用户所属组织机构列表
     */
    List<OrganizationInfoDTO> getOrganizationsByUserId(String userId);

    /**
     * 根据组织机构ID获取该机构下的用户列表
     * @param orgId 组织机构ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户信息分页结果
     */
    Page<UserInfoDTO> getUsersByOrganizationId(String orgId, int page, int size);
}
