package com.sanyth.auth.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.mapper.SytPermissionAccountMapper;
import com.sanyth.auth.server.mapper.SytSysOrganizationMapper;
import com.sanyth.auth.server.mapper.SytSysOrganizationUserMapper;
import com.sanyth.auth.server.model.SytPermissionAccount;
import com.sanyth.auth.server.model.SytSysOrganization;
import com.sanyth.auth.server.service.IThirdPartyApiService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 第三方API服务实现类
 * @since 2025-08-05
 */
@Service
public class ThirdPartyApiServiceImpl implements IThirdPartyApiService {

    @Resource
    private SytPermissionAccountMapper sytPermissionAccountMapper;

    @Resource
    private SytSysOrganizationMapper sytSysOrganizationMapper;

    @Resource
    private SytSysOrganizationUserMapper sytSysOrganizationUserMapper;

    @Override
    public Page<UserInfoDTO> getUserInfoPage(int page, int size, String humancode, String humanname, String orgId) {
        Page<SytPermissionAccount> accountPage = new Page<>(page, size);
        Map<String, Object> queryParam = new HashMap<>();
        if (StringUtils.isNotBlank(humancode)) {
            queryParam.put("humancode", humancode);
        }
        if (StringUtils.isNotBlank(humanname)) {
            queryParam.put("humanname", humanname);
        }
        if (StringUtils.isNotBlank(orgId)) {
            queryParam.put("organizationnames", orgId);
        }
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(queryParam, accountPage);
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }
        Page<UserInfoDTO> resultPage = new Page<>(page, size);
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(accountPage.getTotal());
        resultPage.setCurrent(accountPage.getCurrent());
        resultPage.setSize(accountPage.getSize());
        resultPage.setPages(accountPage.getPages());
        return resultPage;
    }

    @Override
    public UserInfoDTO getUserInfoById(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        SytPermissionAccount account = sytPermissionAccountMapper.selectById(userId);
        if (account == null) {
            return null;
        }
        UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
        List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(userId);
        userInfoDTO.setOrganizations(organizations);
        return userInfoDTO;
    }

    @Override
    public UserInfoDTO getUserInfoByHumancode(String humancode) {
        if (StringUtils.isBlank(humancode)) {
            return null;
        }
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("HUMANCODE", humancode);
        SytPermissionAccount account = sytPermissionAccountMapper.selectOne(wrapper);
        
        if (account == null) {
            return null;
        }
        
        UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
        List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
        userInfoDTO.setOrganizations(organizations);
        
        return userInfoDTO;
    }

    @Override
    public Page<OrganizationInfoDTO> getOrganizationInfoPage(int page, int size, String orgname, String parentId) {
        Page<SytSysOrganization> orgPage = new Page<>(page, size);
        
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(orgname)) {
            wrapper.like("ORGNAME", orgname);
        }
        if (StringUtils.isNotBlank(parentId)) {
            wrapper.eq("PARENT", parentId);
        }
        wrapper.orderByAsc("DISPLAYORDER");
        
        Page<SytSysOrganization> resultPage = sytSysOrganizationMapper.selectPage(orgPage, wrapper);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            for (SytSysOrganization org : resultPage.getRecords()) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        Page<OrganizationInfoDTO> dtoPage = new Page<>(page, size);
        dtoPage.setRecords(orgInfoDTOList);
        dtoPage.setTotal(resultPage.getTotal());
        dtoPage.setCurrent(resultPage.getCurrent());
        dtoPage.setSize(resultPage.getSize());
        dtoPage.setPages(resultPage.getPages());
        
        return dtoPage;
    }

    @Override
    public OrganizationInfoDTO getOrganizationInfoById(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return null;
        }
        
        SytSysOrganization organization = sytSysOrganizationMapper.selectById(orgId);
        if (organization == null) {
            return null;
        }
        
        return convertToOrganizationInfoDTO(organization);
    }

    @Override
    public List<OrganizationInfoDTO> getOrganizationTree(String parentId) {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(parentId)) {
            wrapper.eq("PARENT", parentId);
        } else {
            wrapper.and(w -> w.isNull("PARENT").or().eq("PARENT", ""));
        }
        wrapper.orderByAsc("DISPLAYORDER");
        
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.selectList(wrapper);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                
                // 递归获取子机构
                List<OrganizationInfoDTO> children = getOrganizationTree(org.getId());
                if (!CollectionUtils.isEmpty(children)) {
                    orgInfoDTO.setChildren(children);
                }
                
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    @Override
    public List<OrganizationInfoDTO> getOrganizationsByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new ArrayList<>();
        }
        
        List<SytSysOrganization> orgList = sytSysOrganizationMapper.getOrganizationByUser(userId);
        
        List<OrganizationInfoDTO> orgInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SytSysOrganization org : orgList) {
                OrganizationInfoDTO orgInfoDTO = convertToOrganizationInfoDTO(org);
                orgInfoDTOList.add(orgInfoDTO);
            }
        }
        
        return orgInfoDTOList;
    }

    @Override
    public Page<UserInfoDTO> getUsersByOrganizationId(String orgId, int page, int size) {
        if (StringUtils.isBlank(orgId)) {
            return new Page<>(page, size);
        }
        Page<SytPermissionAccount> accountPage = new Page<>(page, size);
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("organizationnames", orgId);
        
        List<SytPermissionAccount> accountList = sytPermissionAccountMapper.queryList(queryParam, accountPage);
        List<UserInfoDTO> userInfoDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountList)) {
            for (SytPermissionAccount account : accountList) {
                UserInfoDTO userInfoDTO = convertToUserInfoDTO(account);
                List<OrganizationInfoDTO> organizations = getOrganizationsByUserId(account.getId());
                userInfoDTO.setOrganizations(organizations);
                userInfoDTOList.add(userInfoDTO);
            }
        }
        Page<UserInfoDTO> resultPage = new Page<>(page, size);
        resultPage.setRecords(userInfoDTOList);
        resultPage.setTotal(accountPage.getTotal());
        resultPage.setCurrent(accountPage.getCurrent());
        resultPage.setSize(accountPage.getSize());
        resultPage.setPages(accountPage.getPages());
        
        return resultPage;
    }
    private UserInfoDTO convertToUserInfoDTO(SytPermissionAccount account) {
        if (account == null) {
            return null;
        }
        UserInfoDTO dto = new UserInfoDTO();
        BeanUtils.copyProperties(account, dto);
        return dto;
    }
    private OrganizationInfoDTO convertToOrganizationInfoDTO(SytSysOrganization organization) {
        if (organization == null) {
            return null;
        }
        
        OrganizationInfoDTO dto = new OrganizationInfoDTO();
        BeanUtils.copyProperties(organization, dto);
        
        return dto;
    }
}
