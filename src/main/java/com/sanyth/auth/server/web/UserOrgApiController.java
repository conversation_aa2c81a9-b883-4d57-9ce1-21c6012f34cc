package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSONObject;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.ErrorInfo;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.service.IUserOrgApiService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 用户组织机构API前端控制器
 * @since 2025-08-06
 */
@RestController
@RequestMapping("/userOrgApi")
public class UserOrgApiController extends BaseController {

    private Logger log = LoggerFactory.getLogger(UserOrgApiController.class);

    @Autowired
    private IUserOrgApiService iUserOrgApiService;

    /**
     * 分页查询用户信息
     * @param query 查询参数
     * @return 用户信息分页结果
     */
    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<UserInfoDTO> query) {
        try {
            return Resp.success(iUserOrgApiService.queryPage(query));
        } catch (Exception e) {
            log.error("查询用户信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 查询用户信息列表
     * @param userInfoDTO 查询条件
     * @return 用户信息列表
     */
    @PostMapping("/list")
    public Resp list(@RequestBody UserInfoDTO userInfoDTO) {
        try {
            List<UserInfoDTO> list = iUserOrgApiService.queryList(userInfoDTO);
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 分页查询组织机构信息
     * @param query 查询参数
     * @return 组织机构信息分页结果
     */
    @PostMapping("/queryOrgPage")
    public Resp queryOrgPage(@RequestBody BaseQuery<OrganizationInfoDTO> query) {
        try {
            return Resp.success(iUserOrgApiService.queryOrgPage(query));
        } catch (Exception e) {
            log.error("查询组织机构信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 查询组织机构信息列表
     * @param organizationInfoDTO 查询条件
     * @return 组织机构信息列表
     */
    @PostMapping("/orgList")
    public Resp orgList(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            List<OrganizationInfoDTO> list = iUserOrgApiService.queryOrgList(organizationInfoDTO);
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 获取组织机构树形结构
     * @return 组织机构树形结构
     */
    @PostMapping("/treeList")
    public Resp treeList() {
        try {
            List<OrganizationInfoDTO> list = iUserOrgApiService.treeList();
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 根据组织机构ID获取该机构下的用户列表
     * @param query 查询参数
     * @return 用户信息分页结果
     */
    @PostMapping("/getUsersByOrg")
    public Resp getUsersByOrganizationId(@RequestBody BaseQuery<UserInfoDTO> query) {
        try {
            return Resp.success(iUserOrgApiService.getUsersByOrganizationId(query));
        } catch (Exception e) {
            log.error("根据组织机构ID获取用户列表失败", e);
            return Resp.error();
        }
    }

    /**
     * 编辑用户信息
     * @param userInfoDTO 用户信息
     * @return 操作结果
     */
    @PostMapping("/edit")
    public Resp edit(@RequestBody UserInfoDTO userInfoDTO) {
        try {
            // 这里需要实现编辑逻辑，暂时返回成功
            return Resp.success();
        } catch (Exception e) {
            log.error("编辑用户信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 删除用户信息
     * @param param 参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public Resp delete(@RequestBody JSONObject param) {
        String id = param.getString("id");
        try {
            if (StringUtils.isBlank(id)) {
                return Resp.error(ErrorInfo.MSG_0003);
            }
            iUserOrgApiService.removeByIds(Arrays.asList(id.split(",")));
            return Resp.success();
        } catch (Exception e) {
            log.error("删除用户信息出现异常", e);
            return Resp.error();
        }
    }
}
