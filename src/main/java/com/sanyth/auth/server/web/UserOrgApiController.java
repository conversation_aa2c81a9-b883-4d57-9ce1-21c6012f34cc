package com.sanyth.auth.server.web;

import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.BaseQuery;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.service.IUserOrgApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 外部接口用户组织机构
 * 需要走oauth2认证调用
 * @since 2025-08-06
 */
@RestController
@RequestMapping("/api/userOrgApi")
public class UserOrgApiController extends BaseController {

    private Logger log = LoggerFactory.getLogger(UserOrgApiController.class);

    @Autowired
    private IUserOrgApiService iUserOrgApiService;

    /**
     * 分页查询用户信息
     * @param query 查询参数
     * @return 用户信息分页结果
     */
    @PostMapping("/queryPage")
    public Resp queryPage(@RequestBody BaseQuery<UserInfoDTO> query) {
        try {
            return Resp.success(iUserOrgApiService.queryPage(query));
        } catch (Exception e) {
            log.error("查询用户信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 查询用户信息列表
     * @param userInfoDTO 查询条件
     * @return 用户信息列表
     */
    @PostMapping("/list")
    public Resp list(@RequestBody UserInfoDTO userInfoDTO) {
        try {
            List<UserInfoDTO> list = iUserOrgApiService.queryList(userInfoDTO);
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 分页查询组织机构信息
     * @param query 查询参数
     * @return 组织机构信息分页结果
     */
    @PostMapping("/queryOrgPage")
    public Resp queryOrgPage(@RequestBody BaseQuery<OrganizationInfoDTO> query) {
        try {
            return Resp.success(iUserOrgApiService.queryOrgPage(query));
        } catch (Exception e) {
            log.error("查询组织机构信息出现异常", e);
            return Resp.error();
        }
    }

    /**
     * 查询组织机构信息列表
     * @param organizationInfoDTO 查询条件
     * @return 组织机构信息列表
     */
    @PostMapping("/orgList")
    public Resp orgList(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            List<OrganizationInfoDTO> list = iUserOrgApiService.queryOrgList(organizationInfoDTO);
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 获取组织机构树形结构
     * @return 组织机构树形结构
     */
    @PostMapping("/treeList")
    public Resp treeList() {
        try {
            List<OrganizationInfoDTO> list = iUserOrgApiService.treeList();
            return Resp.success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Resp.error();
        }
    }

    /**
     * 根据组织机构ID获取该机构下的用户列表
     * @param query 查询参数
     * @return 用户信息分页结果
     */
    @PostMapping("/getUsersByOrg")
    public Resp getUsersByOrganizationId(@RequestBody BaseQuery<UserInfoDTO> query) {
        try {
            return Resp.success(iUserOrgApiService.getUsersByOrganizationId(query));
        } catch (Exception e) {
            log.error("根据组织机构ID获取用户列表失败", e);
            return Resp.error();
        }
    }

}
