package com.sanyth.auth.server.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.auth.server.core.common.BaseController;
import com.sanyth.auth.server.core.common.Resp;
import com.sanyth.auth.server.dto.OrganizationInfoDTO;
import com.sanyth.auth.server.dto.UserInfoDTO;
import com.sanyth.auth.server.service.IThirdPartyApiService;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 第三方API控制器
 * 提供用户信息和组织机构信息的RESTful API接口
 * 需要OAuth2 access_token认证
 * @since 2025-01-08
 */
@RestController
@RequestMapping("/api/third-party")
public class ThirdPartyApiController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ThirdPartyApiController.class);

    @Resource
    private IThirdPartyApiService thirdPartyApiService;

    // ==================== 用户信息相关接口 ====================

    /**
     * 分页查询用户信息
     * 支持按用户代码、用户名称、组织机构ID进行筛选
     * @param page 页码，默认1
     * @param size 每页大小，默认10，最大100
     * @param humancode 用户代码（可选）
     * @param humanname 用户名称（可选）
     * @param orgId 组织机构ID（可选）
     * @return 用户信息分页结果
     */
    @GetMapping("/users")
    public Resp<Page<UserInfoDTO>> getUserInfoPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String humancode,
            @RequestParam(required = false) String humanname,
            @RequestParam(required = false) String orgId) {
        
        try {
            // 参数校验
            if (page < 1) page = 1;
            if (size < 1 || size > 100) size = 10; // 限制每页最大100条
            
            Page<UserInfoDTO> result = thirdPartyApiService.getUserInfoPage(page, size, humancode, humanname, orgId);
            return Resp.success(result);
        } catch (Exception e) {
            log.error("分页查询用户信息失败", e);
            return Resp.error("查询用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/users/{userId}")
    public Resp<UserInfoDTO> getUserInfoById(@PathVariable String userId) {
        try {
            if (StringUtils.isBlank(userId)) {
                return Resp.error("用户ID不能为空");
            }
            
            UserInfoDTO userInfo = thirdPartyApiService.getUserInfoById(userId);
            if (userInfo == null) {
                return Resp.error("用户不存在");
            }
            
            return Resp.success(userInfo);
        } catch (Exception e) {
            log.error("根据用户ID获取用户信息失败，userId: {}", userId, e);
            return Resp.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户代码获取用户信息
     * @param humancode 用户代码
     * @return 用户信息
     */
    @GetMapping("/users/by-code/{humancode}")
    public Resp<UserInfoDTO> getUserInfoByHumancode(@PathVariable String humancode) {
        try {
            if (StringUtils.isBlank(humancode)) {
                return Resp.error("用户代码不能为空");
            }
            
            UserInfoDTO userInfo = thirdPartyApiService.getUserInfoByHumancode(humancode);
            if (userInfo == null) {
                return Resp.error("用户不存在");
            }
            
            return Resp.success(userInfo);
        } catch (Exception e) {
            log.error("根据用户代码获取用户信息失败，humancode: {}", humancode, e);
            return Resp.error("获取用户信息失败：" + e.getMessage());
        }
    }

    // ==================== 组织机构相关接口 ====================

    /**
     * 分页查询组织机构信息
     * 支持按机构名称、上级机构ID进行筛选
     * @param page 页码，默认1
     * @param size 每页大小，默认10，最大100
     * @param orgname 机构名称（可选）
     * @param parentId 上级机构ID（可选）
     * @return 组织机构信息分页结果
     */
    @GetMapping("/organizations")
    public Resp<Page<OrganizationInfoDTO>> getOrganizationInfoPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String orgname,
            @RequestParam(required = false) String parentId) {
        
        try {
            // 参数校验
            if (page < 1) page = 1;
            if (size < 1 || size > 100) size = 10; // 限制每页最大100条
            
            Page<OrganizationInfoDTO> result = thirdPartyApiService.getOrganizationInfoPage(page, size, orgname, parentId);
            return Resp.success(result);
        } catch (Exception e) {
            log.error("分页查询组织机构信息失败", e);
            return Resp.error("查询组织机构信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据机构ID获取组织机构信息
     * @param orgId 机构ID
     * @return 组织机构信息
     */
    @GetMapping("/organizations/{orgId}")
    public Resp<OrganizationInfoDTO> getOrganizationInfoById(@PathVariable String orgId) {
        try {
            if (StringUtils.isBlank(orgId)) {
                return Resp.error("机构ID不能为空");
            }

            OrganizationInfoDTO orgInfo = thirdPartyApiService.getOrganizationInfoById(orgId);
            if (orgInfo == null) {
                return Resp.error("组织机构不存在");
            }

            return Resp.success(orgInfo);
        } catch (Exception e) {
            log.error("根据机构ID获取组织机构信息失败，orgId: {}", orgId, e);
            return Resp.error("获取组织机构信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取组织机构树形结构
     * 获取完整的组织机构树形结构，支持指定父级机构
     * @param parentId 父级机构ID，为空时获取根节点
     * @return 组织机构树形结构
     */
    @GetMapping("/organizations/tree")
    public Resp<List<OrganizationInfoDTO>> getOrganizationTree(@RequestParam(required = false) String parentId) {
        try {
            List<OrganizationInfoDTO> result = thirdPartyApiService.getOrganizationTree(parentId);
            return Resp.success(result);
        } catch (Exception e) {
            log.error("获取组织机构树形结构失败，parentId: {}", parentId, e);
            return Resp.error("获取组织机构树形结构失败：" + e.getMessage());
        }
    }

    // ==================== 关联查询接口 ====================

    /**
     * 根据用户ID获取用户所属组织机构列表
     * @param userId 用户ID
     * @return 用户所属组织机构列表
     */
    @GetMapping("/users/{userId}/organizations")
    public Resp<List<OrganizationInfoDTO>> getOrganizationsByUserId(@PathVariable String userId) {
        try {
            if (StringUtils.isBlank(userId)) {
                return Resp.error("用户ID不能为空");
            }

            List<OrganizationInfoDTO> result = thirdPartyApiService.getOrganizationsByUserId(userId);
            return Resp.success(result);
        } catch (Exception e) {
            log.error("根据用户ID获取用户所属组织机构列表失败，userId: {}", userId, e);
            return Resp.error("获取用户所属组织机构列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据组织机构ID获取该机构下的用户列表
     * @param orgId 组织机构ID
     * @param page 页码，默认1
     * @param size 每页大小，默认10，最大100
     * @return 用户信息分页结果
     */
    @GetMapping("/organizations/{orgId}/users")
    public Resp<Page<UserInfoDTO>> getUsersByOrganizationId(
            @PathVariable String orgId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        try {
            if (StringUtils.isBlank(orgId)) {
                return Resp.error("组织机构ID不能为空");
            }
            
            // 参数校验
            if (page < 1) page = 1;
            if (size < 1 || size > 100) size = 10; // 限制每页最大100条
            
            Page<UserInfoDTO> result = thirdPartyApiService.getUsersByOrganizationId(orgId, page, size);
            return Resp.success(result);
        } catch (Exception e) {
            log.error("根据组织机构ID获取用户列表失败，orgId: {}", orgId, e);
            return Resp.error("获取用户列表失败：" + e.getMessage());
        }
    }

    // ==================== 健康检查接口 ====================

    /**
     * API健康检查
     * 用于检查第三方API服务是否正常
     * @return 服务状态信息
     */
    @GetMapping("/health")
    public Resp<String> health() {
        return Resp.success("Third Party API Service is running");
    }
}
