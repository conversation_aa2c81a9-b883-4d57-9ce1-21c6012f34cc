package com.sanyth.auth.server.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sanyth.auth.server.core.common.*;
import com.sanyth.auth.server.justauth.JustAuthConstants;
import com.sanyth.auth.server.model.OauthClientDetails;
import com.sanyth.auth.server.model.SytJustAuthConfig;
import com.sanyth.auth.server.model.SytSysParam;
import com.sanyth.auth.server.service.IndexService;
import com.sanyth.auth.server.service.OauthClientDetailsService;
import com.sanyth.auth.server.service.SytJustAuthConfigService;
import com.sanyth.auth.server.service.SytSysParamService;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Controller
@RequestMapping
public class IndexController extends BaseController {

    @Autowired
    SytSysParamService paramService;
    @Autowired
    LoginFailUtils loginFailUtils;
    @Autowired
    IndexService indexService;
    @Autowired
    OauthClientDetailsService oauthClientDetailsService;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    SytJustAuthConfigService sytJustAuthConfigService;

    @RequestMapping
    public String login(HttpServletRequest request, Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof AnonymousAuthenticationToken) {
            getParamData(model);
//            return "login";
            return "loginInfo";
        }
        return "redirect:/index";
    }

    private void getParamData(Model model) {
        SytSysParam param = paramService.get("loginLogo");
        JSONArray array;
        JSONObject object;
        if (param != null) {
            array = JSONArray.parseArray(param.getImg());
            if (array.size() > 0) {
                object = array.getJSONObject(0);
                model.addAttribute("loginLogo", object.getString("url"));
            }
        }
        param = paramService.get("titlelogo");
        if (param != null) {
            array = JSONArray.parseArray(param.getImg());
            if (array.size() > 0) {
                object = array.getJSONObject(0);
                model.addAttribute("titlelogo", object.getString("url"));
            }
        }
        param = paramService.get("loginBackground1");
        if (param != null) {
            array = JSONArray.parseArray(param.getImg());
            if (array.size() > 0) {
                object = array.getJSONObject(0);
                model.addAttribute("loginBackground1", object.getString("url"));
            }
        }
        param = paramService.get("loginBackground2");
        if (param != null) {
            array = JSONArray.parseArray(param.getImg());
            if (array.size() > 0) {
                object = array.getJSONObject(0);
                model.addAttribute("loginBackground2", object.getString("url"));
            }
        }
        param = paramService.get("xtmc");
        if (param != null) {
            model.addAttribute("xtmc", param.getValue());
        }
        param = paramService.get("dbxx");
        if (param != null) {
            model.addAttribute("dbxx", param.getValue());
        }
        param = paramService.get("loginHelp");
        if (param != null) {
            model.addAttribute("loginHelp", param.getValue());
        }
        param = paramService.get("isMobileLogin");
        if (param != null) {
            model.addAttribute("isMobileLogin", param.getValue());
        }
        //todo 页面密码找回方式判断依据
        if (checkSupportEmail()){
            model.addAttribute("emailAuthCode", "block");
        }
        if(checkSupportSms()){
            model.addAttribute("smsAuthCode", "block");
        }
        BaseQuery<SytSysParam> query = new BaseQuery<>();
        SytSysParam sytSysParam = new SytSysParam();
        sytSysParam.setType("promptButton");
        sytSysParam.setStatus(Constants.HAS_YES);
        query.setQueryParam(sytSysParam);
        List<SytSysParam> sytSysParams = paramService.queryList(query);
        if (sytSysParams != null) {
            for (SytSysParam item : sytSysParams) {
                array = JSONArray.parseArray(item.getImg());
                if (array.size() > 0) {
                    object = array.getJSONObject(0);
                    item.setImg(object.getString("url"));
                }
            }
            model.addAttribute("promptButton", sytSysParams);
        }
        SytJustAuthConfig sytJustAuthConfig = sytJustAuthConfigService.getByAuthType(JustAuthConstants.AUTH_TYPE_WECHAT_ENTERPRISE);
        if (sytJustAuthConfig != null) {
            model.addAttribute("agentId", sytJustAuthConfig.getAgentId());
            model.addAttribute("appId", sytJustAuthConfig.getClientId());
            model.addAttribute("callbackUrl", sytJustAuthConfig.getRedirectUri());
        }
    }
    private boolean checkSupportEmail() {
        SytSysParam emailSmtpHost = paramService.get("emailSmtpHost");
        if(emailSmtpHost == null || StringUtils.isEmpty(emailSmtpHost.getValue())){
            return false;
        }
        SytSysParam emailFrom = paramService.get("emailFrom");
        if(emailFrom == null || StringUtils.isEmpty(emailFrom.getValue())){
            return false;
        }
        // 授权码
        SytSysParam emailAuthCode = paramService.get("emailAuthCode");
        if (emailAuthCode == null || StringUtils.isEmpty(emailAuthCode.getValue())) {
            return false;
        }
        return true;
    }
    private boolean checkSupportSms(){
        //todo 手机短信
        //必填:短信签名-可在短信控制台中找到
        String signName = "";
        //必填:短信模板-可在短信控制台中找到，发送国际/港澳台消息时，请使用国际/港澳台短信模版
        String templateCode = "";
        //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
//            String outId = "yourOutId";
        //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
        String templateParamName = "code";

        //替换成你的AK
        String accessKeyId = "";//你的accessKeyId
        String accessKeySecret = "";//你的accessKeySecret

        // TODO 这些参数值从参数表获取
        SytSysParam param = paramService.get("alSms_SignName");
        if (param != null) {
            signName = param.getValue();
        }
        SytSysParam paramTmp = paramService.get("alSms_templateCode_change");
        if (paramTmp != null) {
            param = paramTmp;
        }
        if (param != null) {
            templateCode = param.getValue();
        }
        param = paramService.get("alSms_templateParamName");
        if (param != null) {
            templateParamName = param.getValue();
        }
        param = paramService.get("alSms_accessKeyId");
        if (param != null) {
            accessKeyId = param.getValue();
        }
        param = paramService.get("alSms_accessKeySecret");
        if (param != null) {
            accessKeySecret = param.getValue();
        }
        if (StringUtils.isBlank(signName) || StringUtils.isBlank(templateCode) || StringUtils.isBlank(templateParamName)
                || StringUtils.isBlank(accessKeyId) || StringUtils.isBlank(accessKeySecret)) {
            return false;
        }
        return true;
    }

    @RequestMapping("/index")
    public String toIndex(Model model) {
        SytSysParam param = paramService.get("indexLogo");
        JSONArray array;
        JSONObject object;
        if (param != null) {
            array = JSONArray.parseArray(param.getImg());
            if (array.size() > 0) {
                object = array.getJSONObject(0);
                model.addAttribute("indexLogo", object.getString("url"));
            }
        }
        return "index";
    }

    @RequestMapping("/findPassword")
    public String findPassword(Model model) {
        getParamData(model);
        return "findPassword";
    }

    /**
     * 跳转打开指定页面
     *
     * @param model
     * @param request
     * @return
     */
    @RequestMapping("/redirectPage")
    public String redirectPage(Model model, HttpServletRequest request) {
        String token = request.getParameter("token");
        String url = request.getParameter("url");
        String logout = request.getParameter("logout");
        model.addAttribute("token", token);
        model.addAttribute("url", url);
        model.addAttribute("logout", logout);
        log.info("redirectPage------token:", token);
        log.info("redirectPage------url:", url);
        log.info("redirectPage------logout:", logout);
        return "redirectPage";
    }

    @RequestMapping("/captcha")
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置请求头为输出图片类型
        response.setContentType("image/png");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        // 三个参数分别为宽、高、位数
        SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
        // 设置字体
        //specCaptcha.setFont(new Font("Verdana", Font.PLAIN, 32));  // 有默认字体，可以不用设置
        // 设置类型，纯数字、纯字母、字母数字混合
        specCaptcha.setCharType(Captcha.TYPE_DEFAULT);

        // 验证码存入session
        request.getSession().setAttribute("captcha", specCaptcha.text().toLowerCase());

        // 输出图片流
        specCaptcha.out(response.getOutputStream());
    }

    /**
     * 判断是否需要显示验证码
     *
     * @return
     */
    @RequestMapping(value = "/captchaIsvalid")
    public @ResponseBody boolean isValidCaptcha(HttpServletRequest request, HttpServletResponse response) {
        return loginFailUtils.isValidCaptcha(request, response);
    }

    /**
     * 获取首页数据
     *
     * @return
     */
    @RequestMapping("/getIndexData")
    @ResponseBody
    public Resp indexData() {
        return Resp.success(indexService.queryIndexData());
    }

    /**
     * 经门户打开三方业务系统
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/toClient")
    public void toClient(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String service = request.getParameter("service");   // oauth_client_details表Id
        if (StringUtils.isBlank(service)) {
            Resp.render(response, ErrorInfo.CODE_00001, ErrorInfo.MSG_0003);
        } else {
            OauthClientDetails oauthClientDetails = oauthClientDetailsService.getById(service);
            if (oauthClientDetails == null) {
                Resp.render(response, ErrorInfo.CODE_00001, "error:redirect_uri_mismatch");
                return;
            }
            String url = oauthClientDetails.getWebServerRedirectUri();
            String ticket = UUID.randomUUID().toString();
            CurrentUser currentUser = currentUser(request);
            JSONObject json = new JSONObject();
            json.put("name", currentUser.getHumanname());
            json.put("username", currentUser.getHumancode());
            redisTemplate.opsForValue().set(ticket, json.toJSONString(), 60, TimeUnit.SECONDS);
            url = (url.indexOf("?") != -1 ? url + "&" : url + "?").concat("syt_ticket=").concat(ticket);
            response.sendRedirect(url);
        }
    }

    /**
     * 第三方单点验证接口
     *
     * @param syt_ticket syt票
     * @return {@link Resp}
     * @throws Exception 异常
     */
    @RequestMapping("/serviceValidate")
    @ResponseBody
    public Resp serviceValidate(String syt_ticket) throws Exception {
        if (StringUtils.isBlank(syt_ticket)) {
            return Resp.error(ErrorInfo.MSG_0003);
        }
        Object userinfo = redisTemplate.opsForValue().get(syt_ticket);
        if (userinfo == null) {
            return Resp.error("error:invalid_ticket");
        }
        redisTemplate.delete(syt_ticket);
        return Resp.success(JSON.parseObject(userinfo + ""));
    }

    public static void main(String[] args) {
        try {
            String s = "http://xggl.sanyth.com/wiseduIndex.jsp";
            byte[] bytes = Base64.getEncoder().encode(s.getBytes());
            String encode = URLEncoder.encode(new String(bytes), "UTF-8");
            encode = encode.replace("+", "%20").replace("*", "%2A").replace("~", "%7E").replace("/", "%2F");
            System.out.println(encode);

            String url = URLDecoder.decode(encode, "UTF-8");
            byte[] decode = Base64.getDecoder().decode(url.getBytes());
            System.out.println(new String(decode));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }
}
