package com.sanyth.auth.server.dto;

import java.io.Serializable;

/**
 * 组织机构查询参数DTO
 * @since 2025-08-05
 */
public class OrganizationQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码，默认1
     */
    private int page = 1;

    /**
     * 每页大小，默认10，最大100
     */
    private int size = 10;

    /**
     * 机构名称（支持模糊查询）
     */
    private String orgname;

    /**
     * 上级机构ID
     */
    private String parentId;

    /**
     * 机构类型
     */
    private String categoryId;

    /**
     * 是否有效(0:无效，1:有效)
     */
    private String valid;

    /**
     * 机构编码
     */
    private String code;

    // Getters and Setters
    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = Math.max(1, page);
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = Math.min(Math.max(1, size), 100); // 限制在1-100之间
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
