package com.sanyth.auth.server.dto;

import java.io.Serializable;

/**
 * 用户查询参数DTO
 * @since 2025-08-05
 */
public class UserQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码，默认1
     */
    private int page = 1;

    /**
     * 每页大小，默认10，最大100
     */
    private int size = 10;

    /**
     * 用户代码（支持模糊查询）
     */
    private String humancode;

    /**
     * 用户名称（支持模糊查询）
     */
    private String humanname;

    /**
     * 组织机构ID
     */
    private String orgId;

    /**
     * 性别
     */
    private String sex;

    /**
     * 有效标志
     */
    private Double validflag;

    /**
     * 人员类别
     */
    private String employeeType;

    /**
     * 用户当前状态
     */
    private String currentState;

    // Getters and Setters
    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = Math.max(1, page);
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = Math.min(Math.max(1, size), 100); // 限制在1-100之间
    }

    public String getHumancode() {
        return humancode;
    }

    public void setHumancode(String humancode) {
        this.humancode = humancode;
    }

    public String getHumanname() {
        return humanname;
    }

    public void setHumanname(String humanname) {
        this.humanname = humanname;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Double getValidflag() {
        return validflag;
    }

    public void setValidflag(Double validflag) {
        this.validflag = validflag;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }
}
