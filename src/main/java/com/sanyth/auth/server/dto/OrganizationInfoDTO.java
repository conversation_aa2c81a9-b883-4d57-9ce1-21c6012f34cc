package com.sanyth.auth.server.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 组织机构信息DTO - 用于第三方API接口
 * @since 2025-08-05
 */
public class OrganizationInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String code;
    private String orgname;
    private String orgshortname;
    private String valid;
    private Double displayorder;
    private String orgdescription;
    private String categoryId;
    private String parent;
    private String parents;
    private String orgcode;
    private List<OrganizationInfoDTO> children;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getOrgshortname() {
        return orgshortname;
    }

    public void setOrgshortname(String orgshortname) {
        this.orgshortname = orgshortname;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public Double getDisplayorder() {
        return displayorder;
    }

    public void setDisplayorder(Double displayorder) {
        this.displayorder = displayorder;
    }

    public String getOrgdescription() {
        return orgdescription;
    }

    public void setOrgdescription(String orgdescription) {
        this.orgdescription = orgdescription;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public String getParents() {
        return parents;
    }

    public void setParents(String parents) {
        this.parents = parents;
    }

    public String getOrgcode() {
        return orgcode;
    }

    public void setOrgcode(String orgcode) {
        this.orgcode = orgcode;
    }

    public List<OrganizationInfoDTO> getChildren() {
        return children;
    }

    public void setChildren(List<OrganizationInfoDTO> children) {
        this.children = children;
    }
}
