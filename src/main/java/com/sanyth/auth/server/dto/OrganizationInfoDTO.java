package com.sanyth.auth.server.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 组织机构信息DTO - 用于第三方API接口
 * @since 2025-01-08
 */
@ApiModel(value = "组织机构信息DTO", description = "第三方API组织机构信息响应对象")
public class OrganizationInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机构ID")
    private String id;

    @ApiModelProperty(value = "机构编码")
    private String code;

    @ApiModelProperty(value = "机构名称")
    private String orgname;

    @ApiModelProperty(value = "机构简称")
    private String orgshortname;

    @ApiModelProperty(value = "是否有效(0:无效，1:有效)")
    private String valid;

    @ApiModelProperty(value = "显示顺序")
    private Double displayorder;

    @ApiModelProperty(value = "机构描述")
    private String orgdescription;

    @ApiModelProperty(value = "机构类型")
    private String categoryId;

    @ApiModelProperty(value = "上级机构ID")
    private String parent;

    @ApiModelProperty(value = "所有上级机构ID路径")
    private String parents;

    @ApiModelProperty(value = "机构代码")
    private String orgcode;

    @ApiModelProperty(value = "子机构列表")
    private List<OrganizationInfoDTO> children;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getOrgshortname() {
        return orgshortname;
    }

    public void setOrgshortname(String orgshortname) {
        this.orgshortname = orgshortname;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public Double getDisplayorder() {
        return displayorder;
    }

    public void setDisplayorder(Double displayorder) {
        this.displayorder = displayorder;
    }

    public String getOrgdescription() {
        return orgdescription;
    }

    public void setOrgdescription(String orgdescription) {
        this.orgdescription = orgdescription;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public String getParents() {
        return parents;
    }

    public void setParents(String parents) {
        this.parents = parents;
    }

    public String getOrgcode() {
        return orgcode;
    }

    public void setOrgcode(String orgcode) {
        this.orgcode = orgcode;
    }

    public List<OrganizationInfoDTO> getChildren() {
        return children;
    }

    public void setChildren(List<OrganizationInfoDTO> children) {
        this.children = children;
    }
}
