package com.sanyth.auth.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户信息DTO - 用于第三方API接口，过滤敏感信息
 * @since 2025-08-05
 */
public class UserInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String humancode;
    private String humanname;
    private String humandescription;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validfromdate;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validtodate;
    
    private Double validflag;
    private String sex;
    
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    
    private String teloffice;
    private String telhome;
    private String telmobile1;
    private String telmobile2;
    private String email;
    private String address;
    private String postalcode;
    private Integer age;
    private String orgid;
    private String signature;
    private String idcode;
    private String idtype;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date logintime;
    
    private String logininfo;
    private String dutyid;
    private String humannumber;
    private Double displayorder;
    private String employeeType;
    private String organizationnames;
    private String orgshortname;
    private Long activeflag;
    private String currentState;
    private List<OrganizationInfoDTO> organizations;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHumancode() {
        return humancode;
    }

    public void setHumancode(String humancode) {
        this.humancode = humancode;
    }

    public String getHumanname() {
        return humanname;
    }

    public void setHumanname(String humanname) {
        this.humanname = humanname;
    }

    public String getHumandescription() {
        return humandescription;
    }

    public void setHumandescription(String humandescription) {
        this.humandescription = humandescription;
    }

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    public Date getValidfromdate() {
        return validfromdate;
    }

    public void setValidfromdate(Date validfromdate) {
        this.validfromdate = validfromdate;
    }

    public Date getValidtodate() {
        return validtodate;
    }

    public void setValidtodate(Date validtodate) {
        this.validtodate = validtodate;
    }

    public Double getValidflag() {
        return validflag;
    }

    public void setValidflag(Double validflag) {
        this.validflag = validflag;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getTeloffice() {
        return teloffice;
    }

    public void setTeloffice(String teloffice) {
        this.teloffice = teloffice;
    }

    public String getTelhome() {
        return telhome;
    }

    public void setTelhome(String telhome) {
        this.telhome = telhome;
    }

    public String getTelmobile1() {
        return telmobile1;
    }

    public void setTelmobile1(String telmobile1) {
        this.telmobile1 = telmobile1;
    }

    public String getTelmobile2() {
        return telmobile2;
    }

    public void setTelmobile2(String telmobile2) {
        this.telmobile2 = telmobile2;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostalcode() {
        return postalcode;
    }

    public void setPostalcode(String postalcode) {
        this.postalcode = postalcode;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getIdcode() {
        return idcode;
    }

    public void setIdcode(String idcode) {
        this.idcode = idcode;
    }

    public String getIdtype() {
        return idtype;
    }

    public void setIdtype(String idtype) {
        this.idtype = idtype;
    }

    public Date getLogintime() {
        return logintime;
    }

    public void setLogintime(Date logintime) {
        this.logintime = logintime;
    }

    public String getLogininfo() {
        return logininfo;
    }

    public void setLogininfo(String logininfo) {
        this.logininfo = logininfo;
    }

    public String getDutyid() {
        return dutyid;
    }

    public void setDutyid(String dutyid) {
        this.dutyid = dutyid;
    }

    public String getHumannumber() {
        return humannumber;
    }

    public void setHumannumber(String humannumber) {
        this.humannumber = humannumber;
    }

    public Double getDisplayorder() {
        return displayorder;
    }

    public void setDisplayorder(Double displayorder) {
        this.displayorder = displayorder;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getOrganizationnames() {
        return organizationnames;
    }

    public void setOrganizationnames(String organizationnames) {
        this.organizationnames = organizationnames;
    }

    public String getOrgshortname() {
        return orgshortname;
    }

    public void setOrgshortname(String orgshortname) {
        this.orgshortname = orgshortname;
    }

    public Long getActiveflag() {
        return activeflag;
    }

    public void setActiveflag(Long activeflag) {
        this.activeflag = activeflag;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public List<OrganizationInfoDTO> getOrganizations() {
        return organizations;
    }

    public void setOrganizations(List<OrganizationInfoDTO> organizations) {
        this.organizations = organizations;
    }
}
