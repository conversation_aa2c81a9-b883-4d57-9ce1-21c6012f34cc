package com.sanyth.auth.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户信息DTO - 用于第三方API接口，过滤敏感信息
 * @since 2025-08-05
 */
@Data
public class UserInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String humancode;
    private String humanname;
    private Double validflag;
    private String sex;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    private String teloffice;
    private String telhome;
    private String telmobile1;
    private String telmobile2;
    private String email;
    private String address;
    private String postalcode;
    private Integer age;
    private String orgid;
    private String idcode;
    private String idtype;
    private String dutyid;
    private String employeeType;
    private String organizationnames;
    private String orgshortname;
    private String currentState;
    private List<OrganizationInfoDTO> organizations;
}
