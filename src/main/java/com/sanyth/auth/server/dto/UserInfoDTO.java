package com.sanyth.auth.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户信息DTO - 用于第三方API接口，过滤敏感信息
 * @since 2025-01-08
 */
@ApiModel(value = "用户信息DTO", description = "第三方API用户信息响应对象")
public class UserInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private String id;

    @ApiModelProperty(value = "用户代码/登录账号")
    private String humancode;

    @ApiModelProperty(value = "用户名称")
    private String humanname;

    @ApiModelProperty(value = "用户描述")
    private String humandescription;

    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdate;

    @ApiModelProperty(value = "有效起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validfromdate;

    @ApiModelProperty(value = "有效终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validtodate;

    @ApiModelProperty(value = "有效标志(0:离校, 1:在校)")
    private Double validflag;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty(value = "办公室电话")
    private String teloffice;

    @ApiModelProperty(value = "家庭电话")
    private String telhome;

    @ApiModelProperty(value = "手机号码1")
    private String telmobile1;

    @ApiModelProperty(value = "手机号码2")
    private String telmobile2;

    @ApiModelProperty(value = "电子邮件")
    private String email;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "邮编")
    private String postalcode;

    @ApiModelProperty(value = "排序标识")
    private Integer age;

    @ApiModelProperty(value = "单位标识，多个逗号隔开")
    private String orgid;

    @ApiModelProperty(value = "个性签名")
    private String signature;

    @ApiModelProperty(value = "证件号码")
    private String idcode;

    @ApiModelProperty(value = "证件类型")
    private String idtype;

    @ApiModelProperty(value = "最近登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date logintime;

    @ApiModelProperty(value = "最近登录信息")
    private String logininfo;

    @ApiModelProperty(value = "职务ID")
    private String dutyid;

    @ApiModelProperty(value = "用户工号")
    private String humannumber;

    @ApiModelProperty(value = "显示顺序")
    private Double displayorder;

    @ApiModelProperty(value = "人员类别")
    private String employeeType;

    @ApiModelProperty(value = "用户所属机构名称")
    private String organizationnames;

    @ApiModelProperty(value = "用户所属机构简称")
    private String orgshortname;

    @ApiModelProperty(value = "激活状态")
    private Long activeflag;

    @ApiModelProperty(value = "用户当前状态")
    private String currentState;

    @ApiModelProperty(value = "用户所属组织机构列表")
    private List<OrganizationInfoDTO> organizations;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHumancode() {
        return humancode;
    }

    public void setHumancode(String humancode) {
        this.humancode = humancode;
    }

    public String getHumanname() {
        return humanname;
    }

    public void setHumanname(String humanname) {
        this.humanname = humanname;
    }

    public String getHumandescription() {
        return humandescription;
    }

    public void setHumandescription(String humandescription) {
        this.humandescription = humandescription;
    }

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    public Date getValidfromdate() {
        return validfromdate;
    }

    public void setValidfromdate(Date validfromdate) {
        this.validfromdate = validfromdate;
    }

    public Date getValidtodate() {
        return validtodate;
    }

    public void setValidtodate(Date validtodate) {
        this.validtodate = validtodate;
    }

    public Double getValidflag() {
        return validflag;
    }

    public void setValidflag(Double validflag) {
        this.validflag = validflag;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getTeloffice() {
        return teloffice;
    }

    public void setTeloffice(String teloffice) {
        this.teloffice = teloffice;
    }

    public String getTelhome() {
        return telhome;
    }

    public void setTelhome(String telhome) {
        this.telhome = telhome;
    }

    public String getTelmobile1() {
        return telmobile1;
    }

    public void setTelmobile1(String telmobile1) {
        this.telmobile1 = telmobile1;
    }

    public String getTelmobile2() {
        return telmobile2;
    }

    public void setTelmobile2(String telmobile2) {
        this.telmobile2 = telmobile2;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostalcode() {
        return postalcode;
    }

    public void setPostalcode(String postalcode) {
        this.postalcode = postalcode;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getIdcode() {
        return idcode;
    }

    public void setIdcode(String idcode) {
        this.idcode = idcode;
    }

    public String getIdtype() {
        return idtype;
    }

    public void setIdtype(String idtype) {
        this.idtype = idtype;
    }

    public Date getLogintime() {
        return logintime;
    }

    public void setLogintime(Date logintime) {
        this.logintime = logintime;
    }

    public String getLogininfo() {
        return logininfo;
    }

    public void setLogininfo(String logininfo) {
        this.logininfo = logininfo;
    }

    public String getDutyid() {
        return dutyid;
    }

    public void setDutyid(String dutyid) {
        this.dutyid = dutyid;
    }

    public String getHumannumber() {
        return humannumber;
    }

    public void setHumannumber(String humannumber) {
        this.humannumber = humannumber;
    }

    public Double getDisplayorder() {
        return displayorder;
    }

    public void setDisplayorder(Double displayorder) {
        this.displayorder = displayorder;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getOrganizationnames() {
        return organizationnames;
    }

    public void setOrganizationnames(String organizationnames) {
        this.organizationnames = organizationnames;
    }

    public String getOrgshortname() {
        return orgshortname;
    }

    public void setOrgshortname(String orgshortname) {
        this.orgshortname = orgshortname;
    }

    public Long getActiveflag() {
        return activeflag;
    }

    public void setActiveflag(Long activeflag) {
        this.activeflag = activeflag;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public List<OrganizationInfoDTO> getOrganizations() {
        return organizations;
    }

    public void setOrganizations(List<OrganizationInfoDTO> organizations) {
        this.organizations = organizations;
    }
}
